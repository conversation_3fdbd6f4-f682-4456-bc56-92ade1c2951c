import React, { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { ExternalLink, Download, CheckCircle, AlertCircle, Users, Activity } from 'lucide-react'

const StravaConnect = () => {
  const [isConnecting, setIsConnecting] = useState(false)
  const queryClient = useQueryClient()

  // Check URL parameters for Strava callback
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const stravaConnected = urlParams.get('strava_connected')
    const athleteId = urlParams.get('athlete_id')
    const stravaError = urlParams.get('strava_error')
    const errorMessage = urlParams.get('message')

    if (stravaConnected === 'true' && athleteId) {
      // Clear URL parameters
      window.history.replaceState({}, document.title, window.location.pathname)
      // Refresh data
      queryClient.invalidateQueries(['athletes'])
      queryClient.invalidateQueries(['strava-status'])
    }

    if (stravaError === 'true') {
      alert(`Strava connection error: ${errorMessage || 'Unknown error'}`)
      window.history.replaceState({}, document.title, window.location.pathname)
    }
  }, [queryClient])

  // Get Strava connection status
  const { data: connectionStatus, isLoading: statusLoading } = useQuery({
    queryKey: ['strava-connection'],
    queryFn: async () => {
      const response = await fetch('http://localhost:8000/api/v1/strava/test-connection')
      if (!response.ok) throw new Error('Failed to check connection')
      return response.json()
    }
  })

  // Get list of athletes
  const { data: athletesData, isLoading: athletesLoading } = useQuery({
    queryKey: ['athletes'],
    queryFn: async () => {
      const response = await fetch('http://localhost:8000/api/v1/strava/athletes')
      if (!response.ok) throw new Error('Failed to fetch athletes')
      return response.json()
    }
  })

  // Get auth URL
  const { data: authData, refetch: getAuthUrl } = useQuery({
    queryKey: ['strava-auth-url'],
    queryFn: async () => {
      const response = await fetch('http://localhost:8000/api/v1/strava/auth-url')
      if (!response.ok) throw new Error('Failed to get auth URL')
      return response.json()
    },
    enabled: false
  })

  const handleConnect = async () => {
    setIsConnecting(true)
    try {
      const result = await getAuthUrl()
      if (result.data?.auth_url) {
        window.location.href = result.data.auth_url
      } else if (result.data?.error) {
        alert(`Configuration Error: ${result.data.error}`)
      }
    } catch (error) {
      alert(`Error: ${error.message}`)
    } finally {
      setIsConnecting(false)
    }
  }

  const AthleteCard = ({ athlete }) => {
    const [syncStatus, setSyncStatus] = useState(null)

    // Get sync status for this athlete
    useEffect(() => {
      const fetchSyncStatus = async () => {
        try {
          const response = await fetch(`http://localhost:8000/api/v1/strava/sync-status/${athlete.id}`)
          if (response.ok) {
            const data = await response.json()
            setSyncStatus(data)
          }
        } catch (error) {
          console.error('Error fetching sync status:', error)
        }
      }

      fetchSyncStatus()
    }, [athlete.id])

    return (
      <div className="bg-white rounded-lg shadow p-6 border">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
              <Users className="h-5 w-5 text-white" />
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-semibold text-gray-900">{athlete.name || 'Unknown Athlete'}</h3>
              <p className="text-sm text-gray-500">Strava ID: {athlete.strava_id}</p>
            </div>
          </div>
          <div className="flex items-center">
            {syncStatus?.sync_status === 'completed' ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <AlertCircle className="h-5 w-5 text-yellow-500" />
            )}
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Total Activities:</span>
            <span className="ml-2 font-medium">{athlete.total_activities || 0}</span>
          </div>
          <div>
            <span className="text-gray-500">2025 Activities:</span>
            <span className="ml-2 font-medium text-blue-600">{athlete.activities_2025 || 0}</span>
          </div>
          <div>
            <span className="text-gray-500">Last Sync:</span>
            <span className="ml-2 font-medium">
              {athlete.last_sync ? new Date(athlete.last_sync).toLocaleDateString() : 'Never'}
            </span>
          </div>
          <div>
            <span className="text-gray-500">Status:</span>
            <span className={`ml-2 font-medium ${
              syncStatus?.sync_status === 'completed' ? 'text-green-600' : 'text-yellow-600'
            }`}>
              {syncStatus?.sync_status || 'Unknown'}
            </span>
          </div>
        </div>

        {syncStatus?.latest_activity && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <div className="text-sm">
              <span className="text-gray-500">Latest Activity:</span>
              <span className="ml-2 font-medium">
                {new Date(syncStatus.latest_activity).toLocaleDateString()}
              </span>
            </div>
          </div>
        )}
      </div>
    )
  }

  if (statusLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading Strava configuration...</div>
      </div>
    )
  }

  if (!connectionStatus?.ready) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Strava API Setup Required</h3>
          <p className="text-gray-600 mb-6">
            To download your 2025 activities, you need to configure the Strava API credentials.
          </p>
          
          <div className="bg-gray-50 rounded-lg p-4 text-left">
            <h4 className="font-medium text-gray-900 mb-3">Setup Instructions:</h4>
            <ol className="text-sm text-gray-700 space-y-2">
              {Object.entries(connectionStatus?.setup_instructions || {}).map(([step, instruction]) => (
                <li key={step} className="flex">
                  <span className="font-medium mr-2">{step}.</span>
                  <span>{instruction}</span>
                </li>
              ))}
            </ol>
          </div>

          <div className="mt-6">
            <a
              href="https://developers.strava.com/"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Open Strava Developers
            </a>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Connection Status */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
              <Activity className="h-5 w-5 text-white" />
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-semibold text-gray-900">Strava Integration</h3>
              <p className="text-sm text-gray-500">
                Connect your Strava account to download 2025 activities
              </p>
            </div>
          </div>
          
          <button
            onClick={handleConnect}
            disabled={isConnecting}
            className="flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50"
          >
            {isConnecting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Connecting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Connect Strava
              </>
            )}
          </button>
        </div>
      </div>

      {/* Athletes List */}
      {athletesData?.athletes && athletesData.athletes.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Connected Athletes ({athletesData.total_count})
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {athletesData.athletes.map((athlete) => (
              <AthleteCard key={athlete.id} athlete={athlete} />
            ))}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h4 className="font-medium text-blue-900 mb-2">How it works:</h4>
        <ol className="text-sm text-blue-800 space-y-1">
          <li>1. Click "Connect Strava" to authorize the application</li>
          <li>2. You'll be redirected to Strava to grant permissions</li>
          <li>3. After authorization, your 2025 activities will be downloaded automatically</li>
          <li>4. Use the analytics dashboard to explore your data</li>
        </ol>
      </div>
    </div>
  )
}

export default StravaConnect
