import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import Plot from 'react-plotly.js'
import { 
  Activity, 
  TrendingUp, 
  Target, 
  Zap, 
  Heart, 
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react'

const AdvancedAnalytics = () => {
  const [selectedSport, setSelectedSport] = useState('run')
  const athleteId = 1 // Default athlete ID

  // Get periodization metrics
  const { data: periodizationData, isLoading: periodizationLoading } = useQuery({
    queryKey: ['periodization', athleteId],
    queryFn: async () => {
      const response = await fetch(`http://localhost:8000/api/v1/strava/analytics/periodization/${athleteId}`)
      if (!response.ok) throw new Error('Failed to fetch periodization data')
      return response.json()
    }
  })

  // Get zone analysis for selected sport
  const { data: zoneData, isLoading: zoneLoading } = useQuery({
    queryKey: ['zone-analysis', athleteId, selectedSport],
    queryFn: async () => {
      const response = await fetch(`http://localhost:8000/api/v1/strava/analytics/zone-analysis/${athleteId}?sport_type=${selectedSport}`)
      if (!response.ok) throw new Error('Failed to fetch zone data')
      return response.json()
    }
  })

  // Get zone trends
  const { data: zoneTrends, isLoading: trendsLoading } = useQuery({
    queryKey: ['zone-trends', athleteId, selectedSport],
    queryFn: async () => {
      const response = await fetch(`http://localhost:8000/api/v1/strava/analytics/zone-trends/${athleteId}?sport_type=${selectedSport}`)
      if (!response.ok) throw new Error('Failed to fetch zone trends')
      return response.json()
    },
    enabled: !!zoneData && !zoneData.error
  })

  // Get zone recommendations
  const { data: zoneRecommendations, isLoading: recommendationsLoading } = useQuery({
    queryKey: ['zone-recommendations', athleteId, selectedSport],
    queryFn: async () => {
      const response = await fetch(`http://localhost:8000/api/v1/strava/analytics/zone-recommendations/${athleteId}?sport_type=${selectedSport}`)
      if (!response.ok) throw new Error('Failed to fetch recommendations')
      return response.json()
    },
    enabled: !!zoneData && !zoneData.error
  })

  const MetricCard = ({ title, value, subtitle, icon: Icon, color, alert }) => (
    <div className={`bg-white rounded-lg shadow p-6 border-l-4 ${color}`}>
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center">
            <Icon className="h-5 w-5 text-gray-600 mr-2" />
            <h3 className="text-sm font-medium text-gray-900">{title}</h3>
            {alert && <AlertTriangle className="h-4 w-4 text-orange-500 ml-2" />}
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-2">{value}</p>
          {subtitle && <p className="text-sm text-gray-600 mt-1">{subtitle}</p>}
        </div>
      </div>
    </div>
  )

  const PhaseIndicator = ({ phase, confidence, volumeChange }) => {
    const getPhaseColor = (phase) => {
      switch (phase) {
        case 'taper': return 'bg-blue-100 text-blue-800 border-blue-200'
        case 'build': return 'bg-green-100 text-green-800 border-green-200'
        case 'maintenance': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
        default: return 'bg-gray-100 text-gray-800 border-gray-200'
      }
    }

    const getPhaseIcon = (phase) => {
      switch (phase) {
        case 'taper': return <TrendingUp className="h-4 w-4" />
        case 'build': return <BarChart3 className="h-4 w-4" />
        case 'maintenance': return <CheckCircle className="h-4 w-4" />
        default: return <Clock className="h-4 w-4" />
      }
    }

    return (
      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getPhaseColor(phase)}`}>
        {getPhaseIcon(phase)}
        <span className="ml-2 capitalize">{phase.replace('_', ' ')}</span>
        <span className="ml-2 text-xs">({Math.round(confidence * 100)}%)</span>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Compact Header with Training Load Summary */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-xl font-bold text-gray-900">Advanced Analytics</h2>
          <div className="flex space-x-2">
            <button
              onClick={() => setSelectedSport('run')}
              className={`px-3 py-1 text-sm rounded ${selectedSport === 'run' ? 'bg-red-500 text-white' : 'bg-gray-200 text-gray-700'}`}
            >
              Running
            </button>
            <button
              onClick={() => setSelectedSport('ride')}
              className={`px-3 py-1 text-sm rounded ${selectedSport === 'ride' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'}`}
            >
              Cycling
            </button>
          </div>
        </div>

        {/* Training Load Summary - Top Priority */}
        {periodizationData && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <div className="bg-blue-50 p-3 rounded-lg border-l-4 border-blue-500">
              <div className="text-xs text-blue-600 font-medium">TSB</div>
              <div className="text-lg font-bold text-blue-900">
                {periodizationData.training_stress_balance?.current_tsb?.toFixed(1) || 'N/A'}
              </div>
              <div className="text-xs text-blue-600">stress balance</div>
            </div>
            <div className="bg-green-50 p-3 rounded-lg border-l-4 border-green-500">
              <div className="text-xs text-green-600 font-medium">PHASE</div>
              <div className="text-lg font-bold text-green-900 capitalize">
                {periodizationData.phase_detection?.phase?.replace('_', ' ') || 'Unknown'}
              </div>
              <div className="text-xs text-green-600">
                {(periodizationData.phase_detection?.volume_change * 100)?.toFixed(0)}% change
              </div>
            </div>
            <div className="bg-purple-50 p-3 rounded-lg border-l-4 border-purple-500">
              <div className="text-xs text-purple-600 font-medium">ZONES</div>
              <div className="text-lg font-bold text-purple-900">
                {periodizationData.zone_configuration?.zones_configured ? '✓' : '⚠'}
              </div>
              <div className="text-xs text-purple-600">
                LT: {periodizationData.zone_configuration?.lactate_threshold_hr || 'N/A'}
              </div>
            </div>
            <div className="bg-orange-50 p-3 rounded-lg border-l-4 border-orange-500">
              <div className="text-xs text-orange-600 font-medium">SPORTS</div>
              <div className="text-lg font-bold text-orange-900">
                {Object.keys(periodizationData.sport_tss_breakdown || {}).length}
              </div>
              <div className="text-xs text-orange-600">active sports</div>
            </div>
          </div>
        )}
      </div>

      {/* Compact Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Training Stress Balance Chart */}
        {periodizationData?.training_stress_balance && (
          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="text-sm font-semibold text-gray-900 mb-2">Training Stress Balance (TSB)</h3>
            <Plot
              data={[
                {
                  type: 'scatter',
                  mode: 'lines+markers',
                  x: periodizationData.training_stress_balance.tsb.map(d => d.date),
                  y: periodizationData.training_stress_balance.tsb.map(d => d.value),
                  line: { color: '#3B82F6', width: 2 },
                  marker: { color: '#3B82F6', size: 4 },
                  name: 'TSB',
                  hovertemplate: 'TSB: %{y:.1f}<extra></extra>'
                },
                {
                  type: 'scatter',
                  mode: 'lines',
                  x: periodizationData.training_stress_balance.tsb.map(d => d.date),
                  y: Array(periodizationData.training_stress_balance.tsb.length).fill(0),
                  line: { color: 'rgba(0,0,0,0.3)', width: 1, dash: 'dash' },
                  showlegend: false,
                  hoverinfo: 'skip'
                }
              ]}
              layout={{
                height: 200,
                margin: { t: 10, r: 10, b: 30, l: 30 },
                xaxis: { title: '', tickfont: { size: 10 } },
                yaxis: { title: '', tickfont: { size: 10 } },
                showlegend: false
              }}
              config={{ displayModeBar: false, responsive: true }}
              style={{ width: '100%', height: '200px' }}
            />
          </div>
        )}

        {/* Zone Distribution Table */}
        {zoneData && !zoneData.error && (
          <div className="bg-white rounded-lg shadow p-4">
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-sm font-semibold text-gray-900">
                {zoneData.zone_type || (selectedSport === 'run' ? 'Heart Rate' : 'Power')} Zone Distribution
              </h3>
              <div className="text-xs text-gray-500">
                {selectedSport === 'run' && zoneData.athlete_config?.lactate_threshold_hr && (
                  <span>LT: {zoneData.athlete_config.lactate_threshold_hr} bpm | Max: {zoneData.athlete_config.max_hr || 'N/A'} bpm</span>
                )}
                {selectedSport === 'ride' && zoneData.athlete_config?.ftp && (
                  <span>FTP: {zoneData.athlete_config.ftp} W</span>
                )}
              </div>
            </div>

            {/* Zone Distribution Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Zone</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Range</th>
                    <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Percentage</th>
                    <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                    <th className="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Target</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {Object.entries(zoneData.zone_distribution).map(([zone, percentage]) => {
                    const zoneConfig = zoneData.zones_config[zone];
                    const timeHours = (zoneData.zone_time_seconds[zone] / 3600).toFixed(1);
                    const target = zoneRecommendations?.target_distribution?.[zone] || 0;
                    const isOnTarget = Math.abs(percentage - target) <= 5;

                    return (
                      <tr key={zone} className="hover:bg-gray-50">
                        <td className="px-3 py-2 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className={`w-3 h-3 rounded mr-2 ${
                              zone === 'Z1' ? 'bg-green-500' :
                              zone === 'Z2' ? 'bg-blue-500' :
                              zone === 'Z3' ? 'bg-yellow-500' :
                              zone === 'Z4' ? 'bg-red-500' : 'bg-purple-500'
                            }`}></div>
                            <div>
                              <span className="text-sm font-medium text-gray-900">{zone}</span>
                              <div className="text-xs text-gray-500">
                                {zoneData.zone_definitions?.[zone] || ''}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600">
                          {zoneConfig ? `${Math.round(zoneConfig[0])}-${Math.round(zoneConfig[1])}` : 'N/A'}
                          <span className="text-xs text-gray-400 ml-1">
                            {zoneData.zone_unit || (selectedSport === 'run' ? 'bpm' : 'W')}
                          </span>
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-right">
                          <span className={`text-sm font-medium ${
                            isOnTarget ? 'text-green-600' : 'text-gray-900'
                          }`}>
                            {percentage.toFixed(1)}%
                          </span>
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-right text-sm text-gray-600">
                          {timeHours}h
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-center">
                          <span className={`inline-flex px-2 py-1 text-xs rounded-full ${
                            isOnTarget
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {target}%
                          </span>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
                <tfoot className="bg-gray-50">
                  <tr>
                    <td colSpan="3" className="px-3 py-2 text-sm font-medium text-gray-900">
                      Total Training Time
                    </td>
                    <td className="px-3 py-2 text-right text-sm font-bold text-gray-900">
                      {zoneData.total_time_hours.toFixed(1)}h
                    </td>
                    <td className="px-3 py-2 text-center">
                      <span className={`inline-flex px-2 py-1 text-xs rounded-full ${
                        zoneRecommendations?.training_stress_balance === 'optimal'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {zoneRecommendations?.training_stress_balance === 'optimal' ? '✓ Optimal' : '⚠ Review'}
                      </span>
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>

            {/* Zone Legend */}
            <div className="mt-3 grid grid-cols-2 md:grid-cols-5 gap-2 text-xs">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded mr-1"></div>
                <span className="text-gray-600">Z1: Recovery</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded mr-1"></div>
                <span className="text-gray-600">Z2: Aerobic</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-yellow-500 rounded mr-1"></div>
                <span className="text-gray-600">Z3: Tempo</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-red-500 rounded mr-1"></div>
                <span className="text-gray-600">Z4: Threshold</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-purple-500 rounded mr-1"></div>
                <span className="text-gray-600">Z5: VO2Max</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Zone Configuration Warning */}
      {zoneData?.error && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-4 w-4 text-yellow-600 mr-2" />
            <div>
              <h3 className="text-sm font-medium text-yellow-800">Zone Configuration Required</h3>
              <p className="text-xs text-yellow-700 mt-1">{zoneData.error}</p>
              <p className="text-xs text-yellow-600 mt-1">
                Configure your {selectedSport === 'run' ? 'lactate threshold HR' : 'FTP'} in "Athlete Profile".
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Weekly Zone Trends Table */}
      {zoneTrends && !zoneTrends.error && (
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-sm font-semibold text-gray-900 mb-3">Weekly Zone Trends</h3>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Week</th>
                  <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Z1</th>
                  <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Z2</th>
                  <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Z3</th>
                  <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Z4</th>
                  <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Z5</th>
                  <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Easy%</th>
                  <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Hard%</th>
                  <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Ratio</th>
                  <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Hours</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {zoneTrends.weekly_trends.slice(-8).map((week, index) => {
                  const easyPercent = week.zone_percentages.Z1 + week.zone_percentages.Z2;
                  const hardPercent = week.zone_percentages.Z4 + week.zone_percentages.Z5;
                  const isOptimalRatio = week.polarization_index >= 3 && week.polarization_index <= 5;
                  const isOptimalEasy = easyPercent >= 75 && easyPercent <= 85;

                  return (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                        {week.period}
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap text-right text-sm text-gray-600">
                        {week.zone_percentages.Z1.toFixed(0)}%
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap text-right text-sm text-gray-600">
                        {week.zone_percentages.Z2.toFixed(0)}%
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap text-right text-sm text-gray-600">
                        {week.zone_percentages.Z3.toFixed(0)}%
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap text-right text-sm text-gray-600">
                        {week.zone_percentages.Z4.toFixed(0)}%
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap text-right text-sm text-gray-600">
                        {week.zone_percentages.Z5.toFixed(0)}%
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap text-right">
                        <span className={`text-sm font-medium ${
                          isOptimalEasy ? 'text-green-600' : 'text-yellow-600'
                        }`}>
                          {easyPercent.toFixed(0)}%
                        </span>
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap text-right text-sm text-gray-600">
                        {hardPercent.toFixed(0)}%
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap text-right">
                        <span className={`text-sm font-medium ${
                          isOptimalRatio ? 'text-green-600' : 'text-yellow-600'
                        }`}>
                          {week.polarization_index.toFixed(1)}
                        </span>
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap text-right text-sm text-gray-600">
                        {week.total_time_hours.toFixed(1)}h
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {/* Legend */}
          <div className="mt-3 text-xs text-gray-500">
            <div className="flex flex-wrap gap-4">
              <span><strong>Easy%:</strong> Z1+Z2 (target: 75-85%)</span>
              <span><strong>Hard%:</strong> Z4+Z5 (target: 10-20%)</span>
              <span><strong>Ratio:</strong> Easy/Hard (target: 3-5)</span>
            </div>
          </div>
        </div>
      )}

      {/* Training Recommendations */}
      {zoneRecommendations && !zoneRecommendations.error && (
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-sm font-semibold text-gray-900 mb-3">Training Phase Analysis & Recommendations</h3>

          {/* Current Phase */}
          <div className="mb-4 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-blue-900 capitalize">
                Current Phase: {zoneRecommendations.current_phase.replace('_', ' ')}
              </h4>
              <span className={`px-2 py-1 text-xs rounded ${
                zoneRecommendations.training_stress_balance === 'optimal'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {zoneRecommendations.training_stress_balance === 'optimal' ? '✓ Optimal' : '⚠ Review'}
              </span>
            </div>
            <p className="text-xs text-blue-700 mb-1">{zoneRecommendations.phase_description}</p>
            <p className="text-xs text-blue-600">{zoneRecommendations.phase_focus}</p>
          </div>

          {/* Zone Recommendations */}
          {zoneRecommendations.recommendations.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Zone Adjustments</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {zoneRecommendations.recommendations.map((rec, index) => (
                  <div key={index} className={`p-2 rounded text-xs ${
                    rec.action === 'increase'
                      ? 'bg-green-50 border border-green-200'
                      : 'bg-orange-50 border border-orange-200'
                  }`}>
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{rec.zone}</span>
                      <span className={rec.action === 'increase' ? 'text-green-700' : 'text-orange-700'}>
                        {rec.action === 'increase' ? '↑' : '↓'} {rec.difference.toFixed(1)}%
                      </span>
                    </div>
                    <div className="text-gray-600 mt-1">{rec.suggestion}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Next Phase Suggestion */}
          <div className="p-3 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-1">Next Steps</h4>
            <p className="text-xs text-gray-600">{zoneRecommendations.next_phase_suggestion}</p>
            <div className="mt-2 text-xs text-gray-500">
              Polarization Ratio: {typeof zoneRecommendations.polarization_ratio === 'number'
                ? zoneRecommendations.polarization_ratio.toFixed(1)
                : zoneRecommendations.polarization_ratio}
              (Target: 3-5)
            </div>
          </div>
        </div>
      )}

      {/* Sport TSS Breakdown */}
      {periodizationData?.sport_tss_breakdown && (
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-sm font-semibold text-gray-900 mb-2">Training Stress by Sport</h3>
          <Plot
            data={[{
              type: 'bar',
              x: Object.keys(periodizationData.sport_tss_breakdown).map(sport =>
                sport.replace("root='", "").replace("'", "").toUpperCase()
              ),
              y: Object.values(periodizationData.sport_tss_breakdown),
              marker: {
                color: ['#EF4444', '#3B82F6', '#10B981', '#F59E0B', '#8B5CF6']
              },
              hovertemplate: '%{x}: %{y:.0f} TSS<extra></extra>'
            }]}
            layout={{
              height: 200,
              margin: { t: 10, r: 10, b: 30, l: 30 },
              xaxis: { title: '', tickfont: { size: 10 } },
              yaxis: { title: '', tickfont: { size: 10 } },
              showlegend: false
            }}
            config={{ displayModeBar: false, responsive: true }}
            style={{ width: '100%', height: '200px' }}
          />
        </div>
      )}
    </div>
  )
}

export default AdvancedAnalytics
