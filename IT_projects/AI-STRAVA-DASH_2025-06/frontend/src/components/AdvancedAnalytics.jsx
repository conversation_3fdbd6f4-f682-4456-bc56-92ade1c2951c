import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import Plot from 'react-plotly.js'
import { 
  Activity, 
  TrendingUp, 
  Target, 
  Zap, 
  Heart, 
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react'

const AdvancedAnalytics = () => {
  const [selectedSport, setSelectedSport] = useState('run')
  const athleteId = 1 // Default athlete ID

  // Get periodization metrics
  const { data: periodizationData, isLoading: periodizationLoading } = useQuery({
    queryKey: ['periodization', athleteId],
    queryFn: async () => {
      const response = await fetch(`http://localhost:8000/api/v1/strava/analytics/periodization/${athleteId}`)
      if (!response.ok) throw new Error('Failed to fetch periodization data')
      return response.json()
    }
  })

  // Get zone analysis for selected sport
  const { data: zoneData, isLoading: zoneLoading } = useQuery({
    queryKey: ['zone-analysis', athleteId, selectedSport],
    queryFn: async () => {
      const response = await fetch(`http://localhost:8000/api/v1/strava/analytics/zone-analysis/${athleteId}?sport_type=${selectedSport}`)
      if (!response.ok) throw new Error('Failed to fetch zone data')
      return response.json()
    }
  })

  // Get zone trends
  const { data: zoneTrends, isLoading: trendsLoading } = useQuery({
    queryKey: ['zone-trends', athleteId, selectedSport],
    queryFn: async () => {
      const response = await fetch(`http://localhost:8000/api/v1/strava/analytics/zone-trends/${athleteId}?sport_type=${selectedSport}`)
      if (!response.ok) throw new Error('Failed to fetch zone trends')
      return response.json()
    },
    enabled: !!zoneData && !zoneData.error
  })

  // Get zone recommendations
  const { data: zoneRecommendations, isLoading: recommendationsLoading } = useQuery({
    queryKey: ['zone-recommendations', athleteId, selectedSport],
    queryFn: async () => {
      const response = await fetch(`http://localhost:8000/api/v1/strava/analytics/zone-recommendations/${athleteId}?sport_type=${selectedSport}`)
      if (!response.ok) throw new Error('Failed to fetch recommendations')
      return response.json()
    },
    enabled: !!zoneData && !zoneData.error
  })

  const MetricCard = ({ title, value, subtitle, icon: Icon, color, alert }) => (
    <div className={`bg-white rounded-lg shadow p-6 border-l-4 ${color}`}>
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center">
            <Icon className="h-5 w-5 text-gray-600 mr-2" />
            <h3 className="text-sm font-medium text-gray-900">{title}</h3>
            {alert && <AlertTriangle className="h-4 w-4 text-orange-500 ml-2" />}
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-2">{value}</p>
          {subtitle && <p className="text-sm text-gray-600 mt-1">{subtitle}</p>}
        </div>
      </div>
    </div>
  )

  const PhaseIndicator = ({ phase, confidence, volumeChange }) => {
    const getPhaseColor = (phase) => {
      switch (phase) {
        case 'taper': return 'bg-blue-100 text-blue-800 border-blue-200'
        case 'build': return 'bg-green-100 text-green-800 border-green-200'
        case 'maintenance': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
        default: return 'bg-gray-100 text-gray-800 border-gray-200'
      }
    }

    const getPhaseIcon = (phase) => {
      switch (phase) {
        case 'taper': return <TrendingUp className="h-4 w-4" />
        case 'build': return <BarChart3 className="h-4 w-4" />
        case 'maintenance': return <CheckCircle className="h-4 w-4" />
        default: return <Clock className="h-4 w-4" />
      }
    }

    return (
      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getPhaseColor(phase)}`}>
        {getPhaseIcon(phase)}
        <span className="ml-2 capitalize">{phase.replace('_', ' ')}</span>
        <span className="ml-2 text-xs">({Math.round(confidence * 100)}%)</span>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Compact Header with Training Load Summary */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-xl font-bold text-gray-900">Advanced Analytics</h2>
          <div className="flex space-x-2">
            <button
              onClick={() => setSelectedSport('run')}
              className={`px-3 py-1 text-sm rounded ${selectedSport === 'run' ? 'bg-red-500 text-white' : 'bg-gray-200 text-gray-700'}`}
            >
              Running
            </button>
            <button
              onClick={() => setSelectedSport('ride')}
              className={`px-3 py-1 text-sm rounded ${selectedSport === 'ride' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'}`}
            >
              Cycling
            </button>
          </div>
        </div>

        {/* Training Load Summary - Top Priority */}
        {periodizationData && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <div className="bg-blue-50 p-3 rounded-lg border-l-4 border-blue-500">
              <div className="text-xs text-blue-600 font-medium">TSB</div>
              <div className="text-lg font-bold text-blue-900">
                {periodizationData.training_stress_balance?.current_tsb?.toFixed(1) || 'N/A'}
              </div>
              <div className="text-xs text-blue-600">stress balance</div>
            </div>
            <div className="bg-green-50 p-3 rounded-lg border-l-4 border-green-500">
              <div className="text-xs text-green-600 font-medium">PHASE</div>
              <div className="text-lg font-bold text-green-900 capitalize">
                {periodizationData.phase_detection?.phase?.replace('_', ' ') || 'Unknown'}
              </div>
              <div className="text-xs text-green-600">
                {(periodizationData.phase_detection?.volume_change * 100)?.toFixed(0)}% change
              </div>
            </div>
            <div className="bg-purple-50 p-3 rounded-lg border-l-4 border-purple-500">
              <div className="text-xs text-purple-600 font-medium">ZONES</div>
              <div className="text-lg font-bold text-purple-900">
                {periodizationData.zone_configuration?.zones_configured ? '✓' : '⚠'}
              </div>
              <div className="text-xs text-purple-600">
                LT: {periodizationData.zone_configuration?.lactate_threshold_hr || 'N/A'}
              </div>
            </div>
            <div className="bg-orange-50 p-3 rounded-lg border-l-4 border-orange-500">
              <div className="text-xs text-orange-600 font-medium">SPORTS</div>
              <div className="text-lg font-bold text-orange-900">
                {Object.keys(periodizationData.sport_tss_breakdown || {}).length}
              </div>
              <div className="text-xs text-orange-600">active sports</div>
            </div>
          </div>
        )}
      </div>

      {/* Compact Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Training Stress Balance Chart */}
        {periodizationData?.training_stress_balance && (
          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="text-sm font-semibold text-gray-900 mb-2">Training Stress Balance (TSB)</h3>
            <Plot
              data={[
                {
                  type: 'scatter',
                  mode: 'lines+markers',
                  x: periodizationData.training_stress_balance.tsb.map(d => d.date),
                  y: periodizationData.training_stress_balance.tsb.map(d => d.value),
                  line: { color: '#3B82F6', width: 2 },
                  marker: { color: '#3B82F6', size: 4 },
                  name: 'TSB',
                  hovertemplate: 'TSB: %{y:.1f}<extra></extra>'
                },
                {
                  type: 'scatter',
                  mode: 'lines',
                  x: periodizationData.training_stress_balance.tsb.map(d => d.date),
                  y: Array(periodizationData.training_stress_balance.tsb.length).fill(0),
                  line: { color: 'rgba(0,0,0,0.3)', width: 1, dash: 'dash' },
                  showlegend: false,
                  hoverinfo: 'skip'
                }
              ]}
              layout={{
                height: 200,
                margin: { t: 10, r: 10, b: 30, l: 30 },
                xaxis: { title: '', tickfont: { size: 10 } },
                yaxis: { title: '', tickfont: { size: 10 } },
                showlegend: false
              }}
              config={{ displayModeBar: false, responsive: true }}
              style={{ width: '100%', height: '200px' }}
            />
          </div>
        )}

        {/* Zone Distribution Analysis */}
        {zoneData && !zoneData.error && (
          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="text-sm font-semibold text-gray-900 mb-2">
              {selectedSport === 'run' ? 'HR' : 'Power'} Zone Distribution
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Zone Pie Chart */}
              <div>
                <Plot
                  data={[{
                    type: 'pie',
                    labels: Object.keys(zoneData.zone_distribution),
                    values: Object.values(zoneData.zone_distribution),
                    hole: 0.5,
                    marker: {
                      colors: ['#10B981', '#3B82F6', '#F59E0B', '#EF4444', '#8B5CF6']
                    },
                    textinfo: 'percent',
                    textposition: 'inside',
                    showlegend: false
                  }]}
                  layout={{
                    height: 200,
                    margin: { t: 10, r: 10, b: 10, l: 10 },
                    showlegend: false
                  }}
                  config={{ displayModeBar: false, responsive: true }}
                  style={{ width: '100%', height: '200px' }}
                />
              </div>

              {/* Zone Breakdown */}
              <div className="space-y-2">
                {Object.entries(zoneData.zone_distribution).map(([zone, percentage]) => (
                  <div key={zone} className="flex items-center justify-between text-sm">
                    <div className="flex items-center">
                      <div className={`w-3 h-3 rounded mr-2 ${
                        zone === 'Z1' ? 'bg-green-500' :
                        zone === 'Z2' ? 'bg-blue-500' :
                        zone === 'Z3' ? 'bg-yellow-500' :
                        zone === 'Z4' ? 'bg-red-500' : 'bg-purple-500'
                      }`}></div>
                      <span className="font-medium text-gray-900">{zone}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-gray-900">{percentage.toFixed(1)}%</div>
                      <div className="text-xs text-gray-600">
                        {(zoneData.zone_time_seconds[zone] / 3600).toFixed(1)}h
                      </div>
                    </div>
                  </div>
                ))}
                <div className="pt-2 border-t border-gray-200">
                  <div className="flex justify-between text-xs font-medium text-gray-900">
                    <span>Total:</span>
                    <span>{zoneData.total_time_hours.toFixed(1)}h</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Zone Configuration Warning */}
      {zoneData?.error && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-4 w-4 text-yellow-600 mr-2" />
            <div>
              <h3 className="text-sm font-medium text-yellow-800">Zone Configuration Required</h3>
              <p className="text-xs text-yellow-700 mt-1">{zoneData.error}</p>
              <p className="text-xs text-yellow-600 mt-1">
                Configure your {selectedSport === 'run' ? 'lactate threshold HR' : 'FTP'} in "Athlete Profile".
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Zone Trends and Recommendations */}
      {zoneTrends && !zoneTrends.error && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Weekly Zone Trends */}
          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="text-sm font-semibold text-gray-900 mb-2">Weekly Zone Trends</h3>
            <Plot
              data={[
                {
                  type: 'scatter',
                  mode: 'lines+markers',
                  x: zoneTrends.weekly_trends.map(w => w.period),
                  y: zoneTrends.weekly_trends.map(w => w.zone_percentages.Z1 + w.zone_percentages.Z2),
                  line: { color: '#10B981', width: 2 },
                  marker: { color: '#10B981', size: 4 },
                  name: 'Z1+Z2 (Easy)',
                  hovertemplate: '%{x}<br>Easy: %{y:.1f}%<extra></extra>'
                },
                {
                  type: 'scatter',
                  mode: 'lines+markers',
                  x: zoneTrends.weekly_trends.map(w => w.period),
                  y: zoneTrends.weekly_trends.map(w => w.zone_percentages.Z4 + w.zone_percentages.Z5),
                  line: { color: '#EF4444', width: 2 },
                  marker: { color: '#EF4444', size: 4 },
                  name: 'Z4+Z5 (Hard)',
                  hovertemplate: '%{x}<br>Hard: %{y:.1f}%<extra></extra>'
                }
              ]}
              layout={{
                height: 200,
                margin: { t: 10, r: 10, b: 30, l: 30 },
                xaxis: { title: '', tickfont: { size: 9 }, tickangle: -45 },
                yaxis: { title: '', tickfont: { size: 10 } },
                showlegend: true,
                legend: { x: 0, y: 1, font: { size: 10 } }
              }}
              config={{ displayModeBar: false, responsive: true }}
              style={{ width: '100%', height: '200px' }}
            />
          </div>

          {/* Polarization Index */}
          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="text-sm font-semibold text-gray-900 mb-2">Polarization Index</h3>
            <Plot
              data={[{
                type: 'scatter',
                mode: 'lines+markers',
                x: zoneTrends.weekly_trends.map(w => w.period),
                y: zoneTrends.weekly_trends.map(w => w.polarization_index),
                line: { color: '#8B5CF6', width: 2 },
                marker: { color: '#8B5CF6', size: 4 },
                name: 'Polarization',
                hovertemplate: '%{x}<br>Ratio: %{y:.1f}<extra></extra>'
              }]}
              layout={{
                height: 200,
                margin: { t: 10, r: 10, b: 30, l: 30 },
                xaxis: { title: '', tickfont: { size: 9 }, tickangle: -45 },
                yaxis: { title: '', tickfont: { size: 10 } },
                showlegend: false,
                annotations: [
                  {
                    x: 0.5,
                    y: 0.95,
                    xref: 'paper',
                    yref: 'paper',
                    text: 'Target: 3-5 (80/20 rule)',
                    showarrow: false,
                    font: { size: 10, color: 'gray' }
                  }
                ]
              }}
              config={{ displayModeBar: false, responsive: true }}
              style={{ width: '100%', height: '200px' }}
            />
          </div>
        </div>
      )}

      {/* Training Recommendations */}
      {zoneRecommendations && !zoneRecommendations.error && (
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-sm font-semibold text-gray-900 mb-3">Training Phase Analysis & Recommendations</h3>

          {/* Current Phase */}
          <div className="mb-4 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-blue-900 capitalize">
                Current Phase: {zoneRecommendations.current_phase.replace('_', ' ')}
              </h4>
              <span className={`px-2 py-1 text-xs rounded ${
                zoneRecommendations.training_stress_balance === 'optimal'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {zoneRecommendations.training_stress_balance === 'optimal' ? '✓ Optimal' : '⚠ Review'}
              </span>
            </div>
            <p className="text-xs text-blue-700 mb-1">{zoneRecommendations.phase_description}</p>
            <p className="text-xs text-blue-600">{zoneRecommendations.phase_focus}</p>
          </div>

          {/* Zone Recommendations */}
          {zoneRecommendations.recommendations.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Zone Adjustments</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {zoneRecommendations.recommendations.map((rec, index) => (
                  <div key={index} className={`p-2 rounded text-xs ${
                    rec.action === 'increase'
                      ? 'bg-green-50 border border-green-200'
                      : 'bg-orange-50 border border-orange-200'
                  }`}>
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{rec.zone}</span>
                      <span className={rec.action === 'increase' ? 'text-green-700' : 'text-orange-700'}>
                        {rec.action === 'increase' ? '↑' : '↓'} {rec.difference.toFixed(1)}%
                      </span>
                    </div>
                    <div className="text-gray-600 mt-1">{rec.suggestion}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Next Phase Suggestion */}
          <div className="p-3 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-1">Next Steps</h4>
            <p className="text-xs text-gray-600">{zoneRecommendations.next_phase_suggestion}</p>
            <div className="mt-2 text-xs text-gray-500">
              Polarization Ratio: {typeof zoneRecommendations.polarization_ratio === 'number'
                ? zoneRecommendations.polarization_ratio.toFixed(1)
                : zoneRecommendations.polarization_ratio}
              (Target: 3-5)
            </div>
          </div>
        </div>
      )}

      {/* Sport TSS Breakdown */}
      {periodizationData?.sport_tss_breakdown && (
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-sm font-semibold text-gray-900 mb-2">Training Stress by Sport</h3>
          <Plot
            data={[{
              type: 'bar',
              x: Object.keys(periodizationData.sport_tss_breakdown).map(sport =>
                sport.replace("root='", "").replace("'", "").toUpperCase()
              ),
              y: Object.values(periodizationData.sport_tss_breakdown),
              marker: {
                color: ['#EF4444', '#3B82F6', '#10B981', '#F59E0B', '#8B5CF6']
              },
              hovertemplate: '%{x}: %{y:.0f} TSS<extra></extra>'
            }]}
            layout={{
              height: 200,
              margin: { t: 10, r: 10, b: 30, l: 30 },
              xaxis: { title: '', tickfont: { size: 10 } },
              yaxis: { title: '', tickfont: { size: 10 } },
              showlegend: false
            }}
            config={{ displayModeBar: false, responsive: true }}
            style={{ width: '100%', height: '200px' }}
          />
        </div>
      )}
    </div>
  )
}

export default AdvancedAnalytics
