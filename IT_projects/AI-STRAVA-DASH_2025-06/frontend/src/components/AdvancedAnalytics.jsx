import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import Plot from 'react-plotly.js'
import { 
  Activity, 
  TrendingUp, 
  Target, 
  Zap, 
  Heart, 
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react'

const AdvancedAnalytics = () => {
  const [selectedSport, setSelectedSport] = useState('run')
  const athleteId = 1 // Default athlete ID

  // Get periodization metrics
  const { data: periodizationData, isLoading: periodizationLoading } = useQuery({
    queryKey: ['periodization', athleteId],
    queryFn: async () => {
      const response = await fetch(`http://localhost:8000/api/v1/strava/analytics/periodization/${athleteId}`)
      if (!response.ok) throw new Error('Failed to fetch periodization data')
      return response.json()
    }
  })

  // Get zone analysis for selected sport
  const { data: zoneData, isLoading: zoneLoading } = useQuery({
    queryKey: ['zone-analysis', athleteId, selectedSport],
    queryFn: async () => {
      const response = await fetch(`http://localhost:8000/api/v1/strava/analytics/zone-analysis/${athleteId}?sport_type=${selectedSport}`)
      if (!response.ok) throw new Error('Failed to fetch zone data')
      return response.json()
    }
  })

  const MetricCard = ({ title, value, subtitle, icon: Icon, color, alert }) => (
    <div className={`bg-white rounded-lg shadow p-6 border-l-4 ${color}`}>
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center">
            <Icon className="h-5 w-5 text-gray-600 mr-2" />
            <h3 className="text-sm font-medium text-gray-900">{title}</h3>
            {alert && <AlertTriangle className="h-4 w-4 text-orange-500 ml-2" />}
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-2">{value}</p>
          {subtitle && <p className="text-sm text-gray-600 mt-1">{subtitle}</p>}
        </div>
      </div>
    </div>
  )

  const PhaseIndicator = ({ phase, confidence, volumeChange }) => {
    const getPhaseColor = (phase) => {
      switch (phase) {
        case 'taper': return 'bg-blue-100 text-blue-800 border-blue-200'
        case 'build': return 'bg-green-100 text-green-800 border-green-200'
        case 'maintenance': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
        default: return 'bg-gray-100 text-gray-800 border-gray-200'
      }
    }

    const getPhaseIcon = (phase) => {
      switch (phase) {
        case 'taper': return <TrendingUp className="h-4 w-4" />
        case 'build': return <BarChart3 className="h-4 w-4" />
        case 'maintenance': return <CheckCircle className="h-4 w-4" />
        default: return <Clock className="h-4 w-4" />
      }
    }

    return (
      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getPhaseColor(phase)}`}>
        {getPhaseIcon(phase)}
        <span className="ml-2 capitalize">{phase.replace('_', ' ')}</span>
        <span className="ml-2 text-xs">({Math.round(confidence * 100)}%)</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Advanced Analytics</h2>
        <div className="flex space-x-2">
          <button
            onClick={() => setSelectedSport('run')}
            className={`px-4 py-2 rounded-lg ${selectedSport === 'run' ? 'bg-red-500 text-white' : 'bg-gray-200 text-gray-700'}`}
          >
            Running
          </button>
          <button
            onClick={() => setSelectedSport('ride')}
            className={`px-4 py-2 rounded-lg ${selectedSport === 'ride' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'}`}
          >
            Cycling
          </button>
        </div>
      </div>

      {/* Training Stress Balance Metrics */}
      {periodizationData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="Current TSB"
            value={periodizationData.training_stress_balance?.current_tsb?.toFixed(1) || 'N/A'}
            subtitle="Training Stress Balance"
            icon={Target}
            color="border-blue-500"
            alert={Math.abs(periodizationData.training_stress_balance?.current_tsb || 0) > 25}
          />
          <MetricCard
            title="Training Phase"
            value={<PhaseIndicator {...periodizationData.phase_detection} />}
            subtitle={`Volume change: ${(periodizationData.phase_detection?.volume_change * 100)?.toFixed(1)}%`}
            icon={TrendingUp}
            color="border-green-500"
          />
          <MetricCard
            title="Zone Config"
            value={periodizationData.zone_configuration?.zones_configured ? '✓ Complete' : '⚠ Incomplete'}
            subtitle={`LT: ${periodizationData.zone_configuration?.lactate_threshold_hr || 'Not set'} | FTP: ${periodizationData.zone_configuration?.ftp || 'Not set'}`}
            icon={Heart}
            color="border-purple-500"
            alert={!periodizationData.zone_configuration?.zones_configured}
          />
          <MetricCard
            title="Sport Focus"
            value={Object.keys(periodizationData.sport_tss_breakdown || {}).length}
            subtitle="Active Sports"
            icon={Activity}
            color="border-orange-500"
          />
        </div>
      )}

      {/* Training Stress Balance Chart */}
      {periodizationData?.training_stress_balance && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Training Stress Balance (TSB)</h3>
          <Plot
            data={[
              {
                type: 'scatter',
                mode: 'lines+markers',
                x: periodizationData.training_stress_balance.tsb.map(d => d.date),
                y: periodizationData.training_stress_balance.tsb.map(d => d.value),
                line: { color: '#3B82F6', width: 3 },
                marker: { color: '#3B82F6', size: 6 },
                name: 'TSB',
                hovertemplate: 'Date: %{x}<br>TSB: %{y:.1f}<extra></extra>'
              },
              {
                type: 'scatter',
                mode: 'lines',
                x: periodizationData.training_stress_balance.tsb.map(d => d.date),
                y: Array(periodizationData.training_stress_balance.tsb.length).fill(0),
                line: { color: 'rgba(0,0,0,0.3)', width: 1, dash: 'dash' },
                showlegend: false,
                hoverinfo: 'skip'
              }
            ]}
            layout={{
              height: 300,
              margin: { t: 30, r: 30, b: 50, l: 50 },
              xaxis: { title: 'Date' },
              yaxis: { title: 'Training Stress Balance' },
              showlegend: false,
              annotations: [
                {
                  x: 0.02,
                  y: 0.98,
                  xref: 'paper',
                  yref: 'paper',
                  text: 'Positive TSB = Fresh | Negative TSB = Fatigued',
                  showarrow: false,
                  font: { size: 12, color: 'gray' }
                }
              ]
            }}
            config={{ displayModeBar: false, responsive: true }}
            style={{ width: '100%', height: '300px' }}
          />
        </div>
      )}

      {/* Zone Distribution Analysis */}
      {zoneData && !zoneData.error && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Zone Distribution Pie Chart */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {selectedSport === 'run' ? 'Heart Rate' : 'Power'} Zone Distribution
            </h3>
            <Plot
              data={[{
                type: 'pie',
                labels: Object.keys(zoneData.zone_distribution),
                values: Object.values(zoneData.zone_distribution),
                hole: 0.4,
                marker: {
                  colors: ['#10B981', '#3B82F6', '#F59E0B', '#EF4444', '#8B5CF6']
                },
                textinfo: 'label+percent',
                textposition: 'outside',
                hovertemplate: 'Zone %{label}<br>%{percent}<br>Time: %{value:.1f}%<extra></extra>'
              }]}
              layout={{
                height: 300,
                margin: { t: 30, r: 30, b: 30, l: 30 },
                showlegend: true,
                legend: { orientation: 'h', y: -0.1 }
              }}
              config={{ displayModeBar: false, responsive: true }}
              style={{ width: '100%', height: '300px' }}
            />
          </div>

          {/* Zone Time Breakdown */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Zone Time Analysis</h3>
            <div className="space-y-4">
              {Object.entries(zoneData.zone_distribution).map(([zone, percentage]) => (
                <div key={zone} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-4 h-4 rounded mr-3 ${
                      zone === 'Z1' ? 'bg-green-500' :
                      zone === 'Z2' ? 'bg-blue-500' :
                      zone === 'Z3' ? 'bg-yellow-500' :
                      zone === 'Z4' ? 'bg-red-500' : 'bg-purple-500'
                    }`}></div>
                    <span className="font-medium text-gray-900">{zone}</span>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-gray-900">{percentage.toFixed(1)}%</div>
                    <div className="text-sm text-gray-600">
                      {(zoneData.zone_time_seconds[zone] / 3600).toFixed(1)}h
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex justify-between text-sm font-medium text-gray-900">
                <span>Total Time:</span>
                <span>{zoneData.total_time_hours.toFixed(1)} hours</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Zone Configuration Warning */}
      {zoneData?.error && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mr-3" />
            <div>
              <h3 className="text-lg font-medium text-yellow-800">Zone Configuration Required</h3>
              <p className="text-yellow-700 mt-1">{zoneData.error}</p>
              <p className="text-yellow-600 text-sm mt-2">
                Go to "Athlete Profile" to configure your {selectedSport === 'run' ? 'lactate threshold HR' : 'FTP'} for zone analysis.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Sport TSS Breakdown */}
      {periodizationData?.sport_tss_breakdown && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Training Stress by Sport</h3>
          <Plot
            data={[{
              type: 'bar',
              x: Object.keys(periodizationData.sport_tss_breakdown).map(sport => 
                sport.replace("root='", "").replace("'", "").toUpperCase()
              ),
              y: Object.values(periodizationData.sport_tss_breakdown),
              marker: {
                color: ['#EF4444', '#3B82F6', '#10B981', '#F59E0B', '#8B5CF6']
              },
              hovertemplate: 'Sport: %{x}<br>Total TSS: %{y:.0f}<extra></extra>'
            }]}
            layout={{
              height: 300,
              margin: { t: 30, r: 30, b: 50, l: 50 },
              xaxis: { title: 'Sport' },
              yaxis: { title: 'Total Training Stress Score (TSS)' },
              showlegend: false
            }}
            config={{ displayModeBar: false, responsive: true }}
            style={{ width: '100%', height: '300px' }}
          />
        </div>
      )}
    </div>
  )
}

export default AdvancedAnalytics
