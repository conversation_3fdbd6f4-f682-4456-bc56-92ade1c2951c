import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { User, Heart, Zap, Target, Save } from 'lucide-react'

const AthleteProfile = ({ athleteId }) => {
  const queryClient = useQueryClient()
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({})

  // Fetch athlete data
  const { data: athlete, isLoading } = useQuery({
    queryKey: ['athlete', athleteId],
    queryFn: async () => {
      const response = await fetch(`http://localhost:8000/api/v1/strava/athlete/${athleteId}`)
      if (!response.ok) throw new Error('Failed to fetch athlete')
      return response.json()
    },
    onSuccess: (data) => {
      setFormData(data)
    }
  })

  // Calculate HR zones query
  const { data: hrZones, refetch: calculateHRZones } = useQuery({
    queryKey: ['hr-zones', formData.lactate_threshold_hr, formData.max_hr],
    queryFn: async () => {
      if (!formData.lactate_threshold_hr || !formData.max_hr) return null
      
      const response = await fetch('http://localhost:8000/api/v1/strava/calculate-hr-zones', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          lactate_threshold_hr: formData.lactate_threshold_hr,
          max_hr: formData.max_hr
        })
      })
      if (!response.ok) throw new Error('Failed to calculate HR zones')
      return response.json()
    },
    enabled: !!(formData.lactate_threshold_hr && formData.max_hr)
  })

  // Calculate power zones query
  const { data: powerZones, refetch: calculatePowerZones } = useQuery({
    queryKey: ['power-zones', formData.ftp],
    queryFn: async () => {
      if (!formData.ftp) return null
      
      const response = await fetch(`http://localhost:8000/api/v1/strava/calculate-power-zones?ftp=${formData.ftp}&sport_type=cycling`, {
        method: 'POST'
      })
      if (!response.ok) throw new Error('Failed to calculate power zones')
      return response.json()
    },
    enabled: !!formData.ftp
  })

  // Update athlete mutation
  const updateAthlete = useMutation({
    mutationFn: async (data) => {
      const response = await fetch(`http://localhost:8000/api/v1/strava/athlete/${athleteId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      if (!response.ok) throw new Error('Failed to update athlete')
      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['athlete', athleteId])
      setIsEditing(false)
      // Recalculate zones
      calculateHRZones()
      calculatePowerZones()
    }
  })

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = () => {
    updateAthlete.mutate(formData)
  }

  const ZoneCard = ({ title, zones, unit, icon: Icon, color }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center mb-4">
        <div className={`p-2 rounded-lg ${color}`}>
          <Icon className="h-5 w-5 text-white" />
        </div>
        <h3 className="ml-3 text-lg font-semibold text-gray-900">{title}</h3>
      </div>
      
      {zones ? (
        <div className="space-y-3">
          {Object.entries(zones.zones).map(([zone, range]) => (
            <div key={zone} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="font-medium text-gray-700">Zone {zone}</span>
              <span className="text-gray-600">
                {range.min} - {range.max} {unit}
              </span>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-gray-500 text-center py-8">
          Configure your {title.toLowerCase()} to see zones
        </div>
      )}
    </div>
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading athlete profile...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Athlete Profile</h2>
        <button
          onClick={() => isEditing ? handleSave() : setIsEditing(true)}
          disabled={updateAthlete.isLoading}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          <Save className="h-4 w-4 mr-2" />
          {isEditing ? 'Save Changes' : 'Edit Profile'}
        </button>
      </div>

      {/* Basic Information */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center mb-6">
          <div className="p-3 bg-blue-500 rounded-lg">
            <User className="h-6 w-6 text-white" />
          </div>
          <h3 className="ml-4 text-lg font-semibold text-gray-900">Basic Information</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.first_name || ''}
                onChange={(e) => handleInputChange('first_name', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              />
            ) : (
              <p className="text-gray-900">{athlete?.first_name || 'Not set'}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.last_name || ''}
                onChange={(e) => handleInputChange('last_name', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              />
            ) : (
              <p className="text-gray-900">{athlete?.last_name || 'Not set'}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
            <p className="text-gray-900">{athlete?.email || 'Not set'}</p>
          </div>
        </div>
      </div>

      {/* Physiological Data */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center mb-6">
          <div className="p-3 bg-red-500 rounded-lg">
            <Heart className="h-6 w-6 text-white" />
          </div>
          <h3 className="ml-4 text-lg font-semibold text-gray-900">Physiological Data</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Lactate Threshold HR (bpm)
            </label>
            {isEditing ? (
              <input
                type="number"
                value={formData.lactate_threshold_hr || ''}
                onChange={(e) => handleInputChange('lactate_threshold_hr', parseInt(e.target.value))}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="e.g., 165"
              />
            ) : (
              <p className="text-gray-900">{athlete?.lactate_threshold_hr || 'Not set'}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Max HR (bpm)</label>
            {isEditing ? (
              <input
                type="number"
                value={formData.max_hr || ''}
                onChange={(e) => handleInputChange('max_hr', parseInt(e.target.value))}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="e.g., 190"
              />
            ) : (
              <p className="text-gray-900">{athlete?.max_hr || 'Not set'}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">FTP (watts)</label>
            {isEditing ? (
              <input
                type="number"
                value={formData.ftp || ''}
                onChange={(e) => handleInputChange('ftp', parseFloat(e.target.value))}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="e.g., 250"
              />
            ) : (
              <p className="text-gray-900">{athlete?.ftp || 'Not set'}</p>
            )}
          </div>
        </div>
      </div>

      {/* Training Zones */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ZoneCard
          title="Heart Rate Zones"
          zones={hrZones}
          unit="bpm"
          icon={Heart}
          color="bg-red-500"
        />
        
        <ZoneCard
          title="Power Zones"
          zones={powerZones}
          unit="W"
          icon={Zap}
          color="bg-yellow-500"
        />
      </div>

      {/* Zone Configuration Help */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-center mb-3">
          <Target className="h-5 w-5 text-blue-600 mr-2" />
          <h4 className="text-lg font-semibold text-blue-900">Zone Configuration Guide</h4>
        </div>
        <div className="text-blue-800 space-y-2">
          <p><strong>Lactate Threshold HR:</strong> Your heart rate at lactate threshold (usually from a lab test or field test)</p>
          <p><strong>Max HR:</strong> Your maximum heart rate (220 - age is a rough estimate)</p>
          <p><strong>FTP:</strong> Functional Threshold Power - the highest power you can sustain for 1 hour</p>
        </div>
      </div>
    </div>
  )
}

export default AthleteProfile
