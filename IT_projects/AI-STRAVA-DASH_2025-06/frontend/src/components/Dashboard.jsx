import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import Plot from 'react-plotly.js'
import { TrendingUp, Target, Activity, Zap } from 'lucide-react'

const Dashboard = ({ athleteId }) => {
  const [selectedPeriod, setSelectedPeriod] = useState('30')
  const [selectedSport, setSelectedSport] = useState('all')

  // Zone Distribution Query - disabled until analytics endpoints are implemented
  const { data: zoneData, isLoading: zoneLoading } = useQuery({
    queryKey: ['zone-distribution', athleteId, selectedSport, selectedPeriod],
    queryFn: async () => {
      // Return mock data for now
      return {
        zone_distribution: {
          zone_1_time: 0,
          zone_2_time: 0,
          zone_3_time: 0,
          zone_4_time: 0,
          zone_5_time: 0
        },
        total_time_minutes: 0
      }
    },
    enabled: false // Disable until endpoints are ready
  })

  // Personal Bests Query - disabled until analytics endpoints are implemented
  const { data: pbData, isLoading: pbLoading } = useQuery({
    queryKey: ['personal-bests', athleteId, selectedSport],
    queryFn: async () => {
      return {
        personal_bests: [],
        recent_pbs: [],
        total_pbs: 0
      }
    },
    enabled: false // Disable until endpoints are ready
  })

  // Fatigue Prediction Query - disabled until analytics endpoints are implemented
  const { data: fatigueData, isLoading: fatigueLoading } = useQuery({
    queryKey: ['fatigue-prediction', athleteId],
    queryFn: async () => {
      return {
        current_fatigue_level: 'unknown',
        recommendations: ['Connect your Strava account to get ML-powered insights']
      }
    },
    enabled: false // Disable until endpoints are ready
  })

  // Time Series Query - disabled until analytics endpoints are implemented
  const { data: timeSeriesData, isLoading: tsLoading } = useQuery({
    queryKey: ['time-series', athleteId, 'power', 'WTD'],
    queryFn: async () => {
      return {
        time_series_data: [],
        summary_stats: {
          mean: 0,
          max: 0,
          min: 0,
          trend: 'unknown'
        }
      }
    },
    enabled: false // Disable until endpoints are ready
  })

  // Get Strava connection status and activities
  const { data: stravaData, isLoading: stravaLoading } = useQuery({
    queryKey: ['strava-athletes'],
    queryFn: async () => {
      const response = await fetch('http://localhost:8000/api/v1/strava/athletes')
      if (!response.ok) throw new Error('Failed to fetch Strava data')
      return response.json()
    }
  })

  const StatCard = ({ title, value, icon: Icon, color, subtitle }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div className="ml-4">
          <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
          {subtitle && <p className="text-sm text-gray-600">{subtitle}</p>}
        </div>
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
        <div className="flex space-x-4">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 90 days</option>
          </select>
          <select
            value={selectedSport}
            onChange={(e) => setSelectedSport(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="all">All Sports</option>
            <option value="running">Running</option>
            <option value="cycling">Cycling</option>
            <option value="swimming">Swimming</option>
          </select>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Connected Athletes"
          value={stravaData ? stravaData.total_count || 0 : 0}
          icon={Activity}
          color="bg-blue-500"
          subtitle="Strava Accounts"
        />
        <StatCard
          title="Total Activities"
          value={stravaData?.athletes?.[0]?.total_activities || 0}
          icon={TrendingUp}
          color="bg-green-500"
          subtitle="All Time"
        />
        <StatCard
          title="Recent Activities"
          value={stravaData?.athletes?.[0]?.activities_2025 || 0}
          icon={Zap}
          color="bg-orange-500"
          subtitle="Last 6 Months"
        />
        <StatCard
          title="Sync Status"
          value={stravaData?.athletes?.[0]?.last_sync ? 'Connected' : 'Not Connected'}
          icon={Target}
          color={stravaData?.athletes?.[0]?.last_sync ? 'bg-green-500' : 'bg-red-500'}
          subtitle="Strava Connection"
        />
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Strava Connection Status */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Strava Connection Status</h3>
          {stravaLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-gray-500">Loading Strava data...</div>
            </div>
          ) : stravaData?.athletes?.length > 0 ? (
            <div className="space-y-4">
              {stravaData.athletes.map((athlete) => (
                <div key={athlete.id} className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-green-900">{athlete.name}</h4>
                      <p className="text-sm text-green-700">Strava ID: {athlete.strava_id}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-green-900">{athlete.total_activities} activities</p>
                      <p className="text-xs text-green-600">
                        Last sync: {athlete.last_sync ? new Date(athlete.last_sync).toLocaleDateString() : 'Never'}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="text-gray-500 mb-4">No Strava connection found</div>
                <p className="text-sm text-gray-400">Go to "Connect Strava" tab to link your account</p>
              </div>
            </div>
          )}
        </div>

        {/* Analytics Preview */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Analytics Features</h3>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">🎯 Training Zone Distribution</h4>
              <p className="text-sm text-blue-700">Analyze time spent in different heart rate and power zones</p>
            </div>
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="font-medium text-green-900 mb-2">🏆 Personal Best Tracking</h4>
              <p className="text-sm text-green-700">Automatic detection of PBs across 12+ metrics</p>
            </div>
            <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
              <h4 className="font-medium text-purple-900 mb-2">🧠 ML-Powered Fatigue Prediction</h4>
              <p className="text-sm text-purple-700">AI insights for recovery and training optimization</p>
            </div>
            <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
              <h4 className="font-medium text-orange-900 mb-2">📈 Time Series Analysis</h4>
              <p className="text-sm text-orange-700">Trend analysis with rolling averages and anomaly detection</p>
            </div>
          </div>
        </div>

        {/* Getting Started */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Getting Started</h3>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
              <div>
                <h4 className="font-medium text-gray-900">Connect Strava Account</h4>
                <p className="text-sm text-gray-600">Link your Strava account to download activities</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">2</div>
              <div>
                <h4 className="font-medium text-gray-900">Configure Training Zones</h4>
                <p className="text-sm text-gray-600">Set up your lactate threshold and FTP for accurate analysis</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-medium">3</div>
              <div>
                <h4 className="font-medium text-gray-900">Explore Analytics</h4>
                <p className="text-sm text-gray-600">Discover insights with interactive Plotly visualizations</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Platform Status */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Platform Status</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
              <span className="text-gray-700">Backend API</span>
            </div>
            <span className="text-green-600 font-medium">✓ Running</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
              <span className="text-gray-700">Strava Integration</span>
            </div>
            <span className="text-green-600 font-medium">✓ Configured</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
              <span className="text-gray-700">Analytics Engine</span>
            </div>
            <span className="text-yellow-600 font-medium">⚠ Pending Data</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              <span className="text-gray-700">ML Models</span>
            </div>
            <span className="text-blue-600 font-medium">🔄 Ready</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
