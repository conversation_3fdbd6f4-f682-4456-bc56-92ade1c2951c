import React, { useState } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import Plot from 'react-plotly.js'
import { TrendingUp, Target, Activity, Zap, RefreshCw } from 'lucide-react'

const Dashboard = ({ athleteId }) => {
  const [selectedPeriod, setSelectedPeriod] = useState('30')
  const [selectedSport, setSelectedSport] = useState('all')
  const [isRefreshing, setIsRefreshing] = useState(false)

  const queryClient = useQueryClient()

  // Refresh function to invalidate all queries with current filters
  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      // Invalidate all relevant queries to force refetch with current filters
      await queryClient.invalidateQueries({ queryKey: ['strava-athletes'] })
      await queryClient.invalidateQueries({ queryKey: ['analytics-summary', athleteId, selectedPeriod, selectedSport] })
      await queryClient.invalidateQueries({ queryKey: ['chart-data', athleteId, selectedPeriod, selectedSport] })

      // Wait a bit for the refresh animation
      setTimeout(() => setIsRefreshing(false), 1000)
    } catch (error) {
      console.error('Error refreshing data:', error)
      setIsRefreshing(false)
    }
  }
  const [isRefreshing, setIsRefreshing] = useState(false)

  const queryClient = useQueryClient()

  // Refresh function to invalidate all queries
  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      // Invalidate all relevant queries to force refetch
      await queryClient.invalidateQueries({ queryKey: ['strava-athletes'] })
      await queryClient.invalidateQueries({ queryKey: ['analytics-summary'] })
      await queryClient.invalidateQueries({ queryKey: ['chart-data'] })

      // Wait a bit for the refresh animation
      setTimeout(() => setIsRefreshing(false), 1000)
    } catch (error) {
      console.error('Error refreshing data:', error)
      setIsRefreshing(false)
    }
  }

  // Zone Distribution Query - disabled until analytics endpoints are implemented
  const { data: zoneData, isLoading: zoneLoading } = useQuery({
    queryKey: ['zone-distribution', athleteId, selectedSport, selectedPeriod],
    queryFn: async () => {
      // Return mock data for now
      return {
        zone_distribution: {
          zone_1_time: 0,
          zone_2_time: 0,
          zone_3_time: 0,
          zone_4_time: 0,
          zone_5_time: 0
        },
        total_time_minutes: 0
      }
    },
    enabled: false // Disable until endpoints are ready
  })

  // Personal Bests Query - disabled until analytics endpoints are implemented
  const { data: pbData, isLoading: pbLoading } = useQuery({
    queryKey: ['personal-bests', athleteId, selectedSport],
    queryFn: async () => {
      return {
        personal_bests: [],
        recent_pbs: [],
        total_pbs: 0
      }
    },
    enabled: false // Disable until endpoints are ready
  })

  // Fatigue Prediction Query - disabled until analytics endpoints are implemented
  const { data: fatigueData, isLoading: fatigueLoading } = useQuery({
    queryKey: ['fatigue-prediction', athleteId],
    queryFn: async () => {
      return {
        current_fatigue_level: 'unknown',
        recommendations: ['Connect your Strava account to get ML-powered insights']
      }
    },
    enabled: false // Disable until endpoints are ready
  })

  // Time Series Query - disabled until analytics endpoints are implemented
  const { data: timeSeriesData, isLoading: tsLoading } = useQuery({
    queryKey: ['time-series', athleteId, 'power', 'WTD'],
    queryFn: async () => {
      return {
        time_series_data: [],
        summary_stats: {
          mean: 0,
          max: 0,
          min: 0,
          trend: 'unknown'
        }
      }
    },
    enabled: false // Disable until endpoints are ready
  })

  // Get Strava connection status and activities
  const { data: stravaData, isLoading: stravaLoading } = useQuery({
    queryKey: ['strava-athletes'],
    queryFn: async () => {
      const response = await fetch('http://localhost:8000/api/v1/strava/athletes')
      if (!response.ok) throw new Error('Failed to fetch Strava data')
      return response.json()
    }
  })

  // Get analytics summary
  const { data: analyticsData, isLoading: analyticsLoading } = useQuery({
    queryKey: ['analytics-summary', athleteId, selectedPeriod, selectedSport],
    queryFn: async () => {
      const params = new URLSearchParams({
        period: selectedPeriod,
        sport: selectedSport
      })
      const response = await fetch(`http://localhost:8000/api/v1/strava/analytics/activity-summary/${athleteId}?${params}`)
      if (!response.ok) throw new Error('Failed to fetch analytics data')
      return response.json()
    },
    enabled: !!stravaData?.athletes?.length
  })

  // Get chart data
  const { data: chartData, isLoading: chartLoading } = useQuery({
    queryKey: ['chart-data', athleteId, selectedPeriod, selectedSport],
    queryFn: async () => {
      const params = new URLSearchParams({
        period: selectedPeriod,
        sport: selectedSport
      })
      const response = await fetch(`http://localhost:8000/api/v1/strava/analytics/activity-chart-data/${athleteId}?${params}`)
      if (!response.ok) throw new Error('Failed to fetch chart data')
      return response.json()
    },
    enabled: !!stravaData?.athletes?.length
  })

  const StatCard = ({ title, value, icon: Icon, color, subtitle }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div className="ml-4">
          <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
          {subtitle && <p className="text-sm text-gray-600">{subtitle}</p>}
        </div>
      </div>
    </div>
  )

  return (
    <div className="space-y-4">
      {/* Compact Header with Training Load Summary */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-xl font-bold text-gray-900">Training Dashboard</h2>
          <div className="flex space-x-3">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="border border-gray-300 rounded-md px-2 py-1 text-sm"
            >
              <option value="7">7 days</option>
              <option value="30">30 days</option>
              <option value="90">90 days</option>
            </select>
            <select
              value={selectedSport}
              onChange={(e) => setSelectedSport(e.target.value)}
              className="border border-gray-300 rounded-md px-2 py-1 text-sm"
            >
              <option value="all">All Sports</option>
              <option value="running">Running</option>
              <option value="cycling">Cycling</option>
            </select>
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className={`flex items-center px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                isRefreshing ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'Refreshing...' : 'Refresh'}
            </button>
          </div>
        </div>

        {/* Training Load Summary - Top Priority */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <div className="bg-blue-50 p-3 rounded-lg border-l-4 border-blue-500">
            <div className="text-xs text-blue-600 font-medium">WEEKLY LOAD</div>
            <div className="text-lg font-bold text-blue-900">
              {analyticsData ? `${Math.round(analyticsData.total_activities / 6)}` : '0'}
            </div>
            <div className="text-xs text-blue-600">activities/week</div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg border-l-4 border-green-500">
            <div className="text-xs text-green-600 font-medium">VOLUME</div>
            <div className="text-lg font-bold text-green-900">
              {analyticsData ? `${Math.round(Object.values(analyticsData.sport_breakdown).reduce((sum, sport) => sum + sport.total_time, 0) / 6)}h` : '0h'}
            </div>
            <div className="text-xs text-green-600">hours/week</div>
          </div>
          <div className="bg-orange-50 p-3 rounded-lg border-l-4 border-orange-500">
            <div className="text-xs text-orange-600 font-medium">SPORTS</div>
            <div className="text-lg font-bold text-orange-900">
              {analyticsData ? Object.keys(analyticsData.sport_breakdown).length : 0}
            </div>
            <div className="text-xs text-orange-600">multi-sport</div>
          </div>
          <div className="bg-purple-50 p-3 rounded-lg border-l-4 border-purple-500">
            <div className="text-xs text-purple-600 font-medium">STATUS</div>
            <div className="text-lg font-bold text-purple-900">
              {stravaData?.athletes?.[0]?.last_sync ? '✓' : '⚠'}
            </div>
            <div className="text-xs text-purple-600">sync status</div>
          </div>
        </div>
      </div>

      {/* Compact Charts Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Activity Distribution by Sport */}
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-sm font-semibold text-gray-900 mb-2">Sport Distribution</h3>
          {chartLoading ? (
            <div className="flex items-center justify-center h-40">
              <div className="text-gray-500 text-sm">Loading...</div>
            </div>
          ) : chartData?.sport_distribution ? (
            <Plot
              data={[{
                type: 'pie',
                labels: chartData.sport_distribution.labels.map(l => l.replace("root='", "").replace("'", "")),
                values: chartData.sport_distribution.values,
                hole: 0.5,
                marker: {
                  colors: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6']
                },
                textinfo: 'percent',
                textposition: 'inside',
                showlegend: false
              }]}
              layout={{
                height: 160,
                margin: { t: 10, r: 10, b: 10, l: 10 },
                showlegend: false
              }}
              config={{ displayModeBar: false, responsive: true }}
              style={{ width: '100%', height: '160px' }}
            />
          ) : (
            <div className="flex items-center justify-center h-40">
              <div className="text-gray-500 text-sm">No data</div>
            </div>
          )}
        </div>

        {/* Monthly Activity Trend */}
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-sm font-semibold text-gray-900 mb-2">Monthly Trend</h3>
          {chartLoading ? (
            <div className="flex items-center justify-center h-40">
              <div className="text-gray-500 text-sm">Loading...</div>
            </div>
          ) : chartData?.monthly_trend ? (
            <Plot
              data={[{
                type: 'scatter',
                mode: 'lines+markers',
                x: chartData.monthly_trend.months,
                y: chartData.monthly_trend.counts,
                line: { color: '#3B82F6', width: 2 },
                marker: { color: '#3B82F6', size: 4 },
                name: 'Activities'
              }]}
              layout={{
                height: 160,
                margin: { t: 10, r: 10, b: 30, l: 30 },
                xaxis: { title: '', tickangle: -45, tickfont: { size: 10 } },
                yaxis: { title: '', tickfont: { size: 10 } },
                showlegend: false
              }}
              config={{ displayModeBar: false, responsive: true }}
              style={{ width: '100%', height: '160px' }}
            />
          ) : (
            <div className="flex items-center justify-center h-40">
              <div className="text-gray-500 text-sm">No data</div>
            </div>
          )}
        </div>

        {/* Distance vs Time Scatter Plot */}
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-sm font-semibold text-gray-900 mb-2">Distance vs Time</h3>
          {chartLoading ? (
            <div className="flex items-center justify-center h-40">
              <div className="text-gray-500 text-sm">Loading...</div>
            </div>
          ) : chartData?.distance_time_scatter?.length > 0 ? (
            <Plot
              data={[
                {
                  type: 'scatter',
                  mode: 'markers',
                  x: chartData.distance_time_scatter.filter(d => d.sport === 'run').map(d => d.distance_km),
                  y: chartData.distance_time_scatter.filter(d => d.sport === 'run').map(d => d.time_hours),
                  marker: { color: '#EF4444', size: 4 },
                  name: 'Run',
                  showlegend: false
                },
                {
                  type: 'scatter',
                  mode: 'markers',
                  x: chartData.distance_time_scatter.filter(d => d.sport === 'ride').map(d => d.distance_km),
                  y: chartData.distance_time_scatter.filter(d => d.sport === 'ride').map(d => d.time_hours),
                  marker: { color: '#3B82F6', size: 4 },
                  name: 'Ride',
                  showlegend: false
                }
              ]}
              layout={{
                height: 160,
                margin: { t: 10, r: 10, b: 30, l: 30 },
                xaxis: { title: '', tickfont: { size: 10 } },
                yaxis: { title: '', tickfont: { size: 10 } },
                showlegend: false
              }}
              config={{ displayModeBar: false, responsive: true }}
              style={{ width: '100%', height: '160px' }}
            />
          ) : (
            <div className="flex items-center justify-center h-40">
              <div className="text-gray-500 text-sm">No data</div>
            </div>
          )}
        </div>

        {/* Heart Rate Distribution */}
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-sm font-semibold text-gray-900 mb-2">Heart Rate</h3>
          {chartLoading ? (
            <div className="flex items-center justify-center h-40">
              <div className="text-gray-500 text-sm">Loading...</div>
            </div>
          ) : chartData?.heart_rate_data?.length > 0 ? (
            <Plot
              data={[{
                type: 'histogram',
                x: chartData.heart_rate_data.map(d => d.hr),
                nbinsx: 15,
                marker: { color: '#EF4444', opacity: 0.7 },
                name: 'HR'
              }]}
              layout={{
                height: 160,
                margin: { t: 10, r: 10, b: 30, l: 30 },
                xaxis: { title: '', tickfont: { size: 10 } },
                yaxis: { title: '', tickfont: { size: 10 } },
                showlegend: false
              }}
              config={{ displayModeBar: false, responsive: true }}
              style={{ width: '100%', height: '160px' }}
            />
          ) : (
            <div className="flex items-center justify-center h-40">
              <div className="text-gray-500 text-sm">No data</div>
            </div>
          )}
        </div>
      </div>

      {/* Compact Sport Breakdown & Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Sport Breakdown */}
        {analyticsData && (
          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="text-sm font-semibold text-gray-900 mb-3">Sport Breakdown</h3>
            <div className="grid grid-cols-2 gap-3">
              {Object.entries(analyticsData.sport_breakdown || {}).slice(0, 4).map(([sport, stats]) => (
                <div key={sport} className="p-3 bg-gray-50 rounded-lg">
                  <h4 className="text-xs font-medium text-gray-900 capitalize mb-1">
                    {sport.replace('root=', '').replace(/'/g, '')}
                  </h4>
                  <div className="space-y-1 text-xs text-gray-600">
                    <div className="flex justify-between">
                      <span>Count:</span>
                      <span className="font-medium">{stats.count}</span>
                    </div>
                    {stats.total_distance > 0 && (
                      <div className="flex justify-between">
                        <span>Dist:</span>
                        <span className="font-medium">{stats.total_distance.toFixed(0)}km</span>
                      </div>
                    )}
                    {stats.avg_hr && (
                      <div className="flex justify-between">
                        <span>HR:</span>
                        <span className="font-medium">{Math.round(stats.avg_hr)}bpm</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Platform Status */}
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-sm font-semibold text-gray-900 mb-3">System Status</h3>
          <div className="space-y-2">
            <div className="flex items-center justify-between p-2 bg-green-50 rounded">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <span className="text-xs text-gray-700">Strava Data</span>
              </div>
              <span className="text-xs text-green-600 font-medium">✓ {analyticsData?.total_activities || 0}</span>
            </div>
            <div className="flex items-center justify-between p-2 bg-green-50 rounded">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <span className="text-xs text-gray-700">Charts</span>
              </div>
              <span className="text-xs text-green-600 font-medium">✓ Active</span>
            </div>
            <div className="flex items-center justify-between p-2 bg-blue-50 rounded">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                <span className="text-xs text-gray-700">Analytics</span>
              </div>
              <span className="text-xs text-blue-600 font-medium">🔄 Ready</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
