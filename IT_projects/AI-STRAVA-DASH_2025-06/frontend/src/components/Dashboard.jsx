import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import Plot from 'react-plotly.js'
import { TrendingUp, Target, Activity, Zap } from 'lucide-react'

const Dashboard = ({ athleteId }) => {
  const [selectedPeriod, setSelectedPeriod] = useState('30')
  const [selectedSport, setSelectedSport] = useState('all')

  // Zone Distribution Query - disabled until analytics endpoints are implemented
  const { data: zoneData, isLoading: zoneLoading } = useQuery({
    queryKey: ['zone-distribution', athleteId, selectedSport, selectedPeriod],
    queryFn: async () => {
      // Return mock data for now
      return {
        zone_distribution: {
          zone_1_time: 0,
          zone_2_time: 0,
          zone_3_time: 0,
          zone_4_time: 0,
          zone_5_time: 0
        },
        total_time_minutes: 0
      }
    },
    enabled: false // Disable until endpoints are ready
  })

  // Personal Bests Query - disabled until analytics endpoints are implemented
  const { data: pbData, isLoading: pbLoading } = useQuery({
    queryKey: ['personal-bests', athleteId, selectedSport],
    queryFn: async () => {
      return {
        personal_bests: [],
        recent_pbs: [],
        total_pbs: 0
      }
    },
    enabled: false // Disable until endpoints are ready
  })

  // Fatigue Prediction Query - disabled until analytics endpoints are implemented
  const { data: fatigueData, isLoading: fatigueLoading } = useQuery({
    queryKey: ['fatigue-prediction', athleteId],
    queryFn: async () => {
      return {
        current_fatigue_level: 'unknown',
        recommendations: ['Connect your Strava account to get ML-powered insights']
      }
    },
    enabled: false // Disable until endpoints are ready
  })

  // Time Series Query - disabled until analytics endpoints are implemented
  const { data: timeSeriesData, isLoading: tsLoading } = useQuery({
    queryKey: ['time-series', athleteId, 'power', 'WTD'],
    queryFn: async () => {
      return {
        time_series_data: [],
        summary_stats: {
          mean: 0,
          max: 0,
          min: 0,
          trend: 'unknown'
        }
      }
    },
    enabled: false // Disable until endpoints are ready
  })

  // Get Strava connection status and activities
  const { data: stravaData, isLoading: stravaLoading } = useQuery({
    queryKey: ['strava-athletes'],
    queryFn: async () => {
      const response = await fetch('http://localhost:8000/api/v1/strava/athletes')
      if (!response.ok) throw new Error('Failed to fetch Strava data')
      return response.json()
    }
  })

  // Get analytics summary
  const { data: analyticsData, isLoading: analyticsLoading } = useQuery({
    queryKey: ['analytics-summary', athleteId],
    queryFn: async () => {
      const response = await fetch(`http://localhost:8000/api/v1/strava/analytics/activity-summary/${athleteId}`)
      if (!response.ok) throw new Error('Failed to fetch analytics data')
      return response.json()
    },
    enabled: !!stravaData?.athletes?.length
  })

  // Get chart data
  const { data: chartData, isLoading: chartLoading } = useQuery({
    queryKey: ['chart-data', athleteId],
    queryFn: async () => {
      const response = await fetch(`http://localhost:8000/api/v1/strava/analytics/activity-chart-data/${athleteId}`)
      if (!response.ok) throw new Error('Failed to fetch chart data')
      return response.json()
    },
    enabled: !!stravaData?.athletes?.length
  })

  const StatCard = ({ title, value, icon: Icon, color, subtitle }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div className="ml-4">
          <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
          {subtitle && <p className="text-sm text-gray-600">{subtitle}</p>}
        </div>
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
        <div className="flex space-x-4">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 90 days</option>
          </select>
          <select
            value={selectedSport}
            onChange={(e) => setSelectedSport(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="all">All Sports</option>
            <option value="running">Running</option>
            <option value="cycling">Cycling</option>
            <option value="swimming">Swimming</option>
          </select>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Activities"
          value={analyticsData?.total_activities || 0}
          icon={Activity}
          color="bg-blue-500"
          subtitle="All Sports"
        />
        <StatCard
          title="Sports Tracked"
          value={analyticsData?.sport_breakdown ? Object.keys(analyticsData.sport_breakdown).length : 0}
          icon={TrendingUp}
          color="bg-green-500"
          subtitle="Different Activities"
        />
        <StatCard
          title="This Month"
          value={analyticsData?.monthly_trends ?
            Object.values(analyticsData.monthly_trends).reduce((sum, month) => sum + month.count, 0) : 0}
          icon={Zap}
          color="bg-orange-500"
          subtitle="Activities"
        />
        <StatCard
          title="Data Quality"
          value={stravaData?.athletes?.[0]?.last_sync ? '✓ Excellent' : '⚠ Needs Sync'}
          icon={Target}
          color={stravaData?.athletes?.[0]?.last_sync ? 'bg-green-500' : 'bg-red-500'}
          subtitle="Strava Connection"
        />
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Activity Distribution by Sport */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Activity Distribution by Sport</h3>
          {chartLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-gray-500">Loading chart data...</div>
            </div>
          ) : chartData?.sport_distribution ? (
            <Plot
              data={[{
                type: 'pie',
                labels: chartData.sport_distribution.labels,
                values: chartData.sport_distribution.values,
                hole: 0.4,
                marker: {
                  colors: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4']
                },
                textinfo: 'label+percent',
                textposition: 'outside'
              }]}
              layout={{
                height: 300,
                margin: { t: 30, r: 30, b: 30, l: 30 },
                showlegend: true,
                legend: { orientation: 'h', y: -0.1 }
              }}
              config={{ displayModeBar: false, responsive: true }}
              style={{ width: '100%', height: '300px' }}
            />
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-gray-500">No activity data available</div>
            </div>
          )}
        </div>

        {/* Monthly Activity Trend */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Monthly Activity Trend</h3>
          {chartLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-gray-500">Loading trend data...</div>
            </div>
          ) : chartData?.monthly_trend ? (
            <Plot
              data={[{
                type: 'scatter',
                mode: 'lines+markers',
                x: chartData.monthly_trend.months,
                y: chartData.monthly_trend.counts,
                line: { color: '#3B82F6', width: 3 },
                marker: { color: '#3B82F6', size: 8 },
                name: 'Activities per Month'
              }]}
              layout={{
                height: 300,
                margin: { t: 30, r: 30, b: 50, l: 50 },
                xaxis: { title: 'Month', tickangle: -45 },
                yaxis: { title: 'Number of Activities' },
                showlegend: false
              }}
              config={{ displayModeBar: false, responsive: true }}
              style={{ width: '100%', height: '300px' }}
            />
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-gray-500">No trend data available</div>
            </div>
          )}
        </div>

        {/* Distance vs Time Scatter Plot */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Distance vs Time Analysis</h3>
          {chartLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-gray-500">Loading scatter data...</div>
            </div>
          ) : chartData?.distance_time_scatter?.length > 0 ? (
            <Plot
              data={[
                {
                  type: 'scatter',
                  mode: 'markers',
                  x: chartData.distance_time_scatter.filter(d => d.sport === 'run').map(d => d.distance_km),
                  y: chartData.distance_time_scatter.filter(d => d.sport === 'run').map(d => d.time_hours),
                  marker: { color: '#EF4444', size: 8 },
                  name: 'Running',
                  text: chartData.distance_time_scatter.filter(d => d.sport === 'run').map(d => d.name),
                  hovertemplate: '<b>%{text}</b><br>Distance: %{x:.1f} km<br>Time: %{y:.2f} hours<extra></extra>'
                },
                {
                  type: 'scatter',
                  mode: 'markers',
                  x: chartData.distance_time_scatter.filter(d => d.sport === 'ride').map(d => d.distance_km),
                  y: chartData.distance_time_scatter.filter(d => d.sport === 'ride').map(d => d.time_hours),
                  marker: { color: '#3B82F6', size: 8 },
                  name: 'Cycling',
                  text: chartData.distance_time_scatter.filter(d => d.sport === 'ride').map(d => d.name),
                  hovertemplate: '<b>%{text}</b><br>Distance: %{x:.1f} km<br>Time: %{y:.2f} hours<extra></extra>'
                }
              ]}
              layout={{
                height: 300,
                margin: { t: 30, r: 30, b: 50, l: 50 },
                xaxis: { title: 'Distance (km)' },
                yaxis: { title: 'Time (hours)' },
                showlegend: true,
                legend: { x: 0, y: 1 }
              }}
              config={{ displayModeBar: false, responsive: true }}
              style={{ width: '100%', height: '300px' }}
            />
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-gray-500">No distance/time data available</div>
            </div>
          )}
        </div>

        {/* Heart Rate Distribution */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Heart Rate Distribution</h3>
          {chartLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-gray-500">Loading heart rate data...</div>
            </div>
          ) : chartData?.heart_rate_data?.length > 0 ? (
            <Plot
              data={[{
                type: 'histogram',
                x: chartData.heart_rate_data.map(d => d.hr),
                nbinsx: 20,
                marker: { color: '#EF4444', opacity: 0.7 },
                name: 'Heart Rate Distribution'
              }]}
              layout={{
                height: 300,
                margin: { t: 30, r: 30, b: 50, l: 50 },
                xaxis: { title: 'Average Heart Rate (bpm)' },
                yaxis: { title: 'Number of Activities' },
                showlegend: false
              }}
              config={{ displayModeBar: false, responsive: true }}
              style={{ width: '100%', height: '300px' }}
            />
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-gray-500">No heart rate data available</div>
            </div>
          )}
        </div>
      </div>

      {/* Activity Insights */}
      {analyticsData && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Activity Insights</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(analyticsData.sport_breakdown || {}).map(([sport, stats]) => (
              <div key={sport} className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-900 capitalize mb-2">{sport.replace('root=', '').replace(/'/g, '')}</h4>
                <div className="space-y-1 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>Activities:</span>
                    <span className="font-medium">{stats.count}</span>
                  </div>
                  {stats.total_distance > 0 && (
                    <div className="flex justify-between">
                      <span>Distance:</span>
                      <span className="font-medium">{stats.total_distance.toFixed(1)} km</span>
                    </div>
                  )}
                  {stats.total_time > 0 && (
                    <div className="flex justify-between">
                      <span>Time:</span>
                      <span className="font-medium">{stats.total_time.toFixed(1)} hrs</span>
                    </div>
                  )}
                  {stats.avg_hr && (
                    <div className="flex justify-between">
                      <span>Avg HR:</span>
                      <span className="font-medium">{Math.round(stats.avg_hr)} bpm</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Platform Status */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Platform Status</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
              <span className="text-gray-700">Strava Data</span>
            </div>
            <span className="text-green-600 font-medium">✓ {analyticsData?.total_activities || 0} Activities</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
              <span className="text-gray-700">Interactive Charts</span>
            </div>
            <span className="text-green-600 font-medium">✓ Plotly Enabled</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
              <span className="text-gray-700">Analytics Engine</span>
            </div>
            <span className="text-green-600 font-medium">✓ Active</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              <span className="text-gray-700">ML Models</span>
            </div>
            <span className="text-blue-600 font-medium">🔄 Ready for Training Zones</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
