import React, { useState } from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import Dashboard from './components/Dashboard'
import AthleteProfile from './components/AthleteProfile'
import StravaConnect from './components/StravaConnect'
import AdvancedAnalytics from './components/AdvancedAnalytics'
import { Activity, User, BarChart3, Target, Download, TrendingUp } from 'lucide-react'
import './App.css'

// Create a client
const queryClient = new QueryClient()

function App() {
  const [activeTab, setActiveTab] = useState('strava')
  const [athleteId, setAthleteId] = useState(1) // Default athlete ID

  const tabs = [
    { id: 'strava', label: 'Connect Strava', icon: Download },
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
    { id: 'analytics', label: 'Advanced Analytics', icon: TrendingUp },
    { id: 'profile', label: 'Athlete Profile', icon: User },
    { id: 'zones', label: 'Training Zones', icon: Target },
    { id: 'activities', label: 'Activities', icon: Activity }
  ]

  const renderContent = () => {
    switch (activeTab) {
      case 'strava':
        return <StravaConnect />
      case 'dashboard':
        return <Dashboard athleteId={athleteId} />
      case 'analytics':
        return <AdvancedAnalytics />
      case 'profile':
        return <AthleteProfile athleteId={athleteId} />
      case 'zones':
        return <div className="p-6">Training Zones (Coming Soon)</div>
      case 'activities':
        return <div className="p-6">Activities (Coming Soon)</div>
      default:
        return <StravaConnect />
    }
  }

  return (
    <QueryClientProvider client={queryClient}>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <BarChart3 className="h-8 w-8 text-blue-600 mr-3" />
                <h1 className="text-xl font-bold text-gray-900">
                  Fitness Analytics Platform
                </h1>
              </div>
              <div className="text-sm text-gray-500">
                State-of-the-Art Analytics with ML Insights
              </div>
            </div>
          </div>
        </header>

        {/* Navigation */}
        <nav className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center px-3 py-4 text-sm font-medium border-b-2 transition-colors ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="h-4 w-4 mr-2" />
                    {tab.label}
                  </button>
                )
              })}
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          {renderContent()}
        </main>
      </div>
    </QueryClientProvider>
  )
}

export default App
