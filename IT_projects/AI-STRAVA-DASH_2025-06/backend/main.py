"""
State-of-the-Art Fitness Analytics Platform
Main FastAPI application entry point
"""

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from contextlib import asynccontextmanager
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import API router
from app.api.v1.api import api_router

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    print("🚀 Starting Fitness Analytics Platform...")
    print(f"Strava Client ID configured: {bool(os.getenv('STRAVA_CLIENT_ID'))}")
    yield
    # Shutdown
    print("🛑 Shutting down Fitness Analytics Platform...")

# Create FastAPI application
app = FastAPI(
    title="Fitness Analytics Platform API",
    description="State-of-the-art fitness analytics with ML-powered insights",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(api_router, prefix="/api/v1")

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Fitness Analytics Platform API",
        "version": "1.0.0",
        "status": "active",
        "features": [
            "Time-series analysis",
            "Personal best tracking", 
            "Training zone optimization",
            "ML-powered fatigue modeling",
            "Interactive Plotly dashboards"
        ]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": "2025-06-20"}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
