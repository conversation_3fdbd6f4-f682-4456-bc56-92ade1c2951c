"""
API v1 router configuration
"""

from fastapi import APIRouter
from app.api.v1.endpoints import strava_simple

api_router = APIRouter()

# Include Strava endpoints for testing
api_router.include_router(
    strava_simple.router,
    prefix="/strava",
    tags=["strava"]
)

# Include athlete profile endpoints (using simple database)
api_router.include_router(
    strava_simple.router,
    prefix="/athletes",
    tags=["athletes"]
)

# Include training zone endpoints (using simple database)
api_router.include_router(
    strava_simple.router,
    prefix="/training-zones",
    tags=["training-zones"]
)

# TODO: Add analytics endpoints when database is properly configured
# api_router.include_router(analytics.router, prefix="/analytics", tags=["analytics"])
# api_router.include_router(activities.router, prefix="/activities", tags=["activities"])
