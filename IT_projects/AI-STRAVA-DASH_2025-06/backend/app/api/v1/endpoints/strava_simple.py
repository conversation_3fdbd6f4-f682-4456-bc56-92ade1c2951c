"""
Simplified Strava endpoints using simple database
"""

from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from fastapi.responses import RedirectResponse
from typing import List, Dict
import os
from datetime import datetime
import logging

from app.core.simple_db import get_db
from app.services.strava_service import StravaService

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/auth-url")
async def get_strava_auth_url():
    """
    Get Strava authorization URL to start OAuth flow
    """
    strava_service = StravaService()
    
    if not strava_service.client_id:
        return {
            "error": "Strava API not configured",
            "instructions": "Please set STRAVA_CLIENT_ID and STRAVA_CLIENT_SECRET environment variables",
            "setup_guide": {
                "step_1": "Go to https://developers.strava.com/",
                "step_2": "Create an application",
                "step_3": "Copy Client ID and Client Secret",
                "step_4": "Set environment variables in .env file"
            }
        }
    
    auth_url = strava_service.get_authorization_url()
    
    return {
        "auth_url": auth_url,
        "instructions": "Visit this URL to authorize the application with your Strava account"
    }

@router.get("/callback")
async def strava_callback(
    code: str = Query(..., description="Authorization code from Strava"),
    scope: str = Query(None, description="Granted scopes"),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """
    Handle Strava OAuth callback and exchange code for token
    """
    strava_service = StravaService()
    db = get_db()
    
    try:
        # Exchange code for token
        token_data = strava_service.exchange_code_for_token(code)

        # Get athlete info - try from token first, then API call
        athlete_info = None
        if token_data.get('athlete_info'):
            # Use athlete info from token response
            athlete_data = token_data['athlete_info']
            athlete_info = {
                'strava_id': str(athlete_data.get('id', '')),
                'first_name': athlete_data.get('firstname', ''),
                'last_name': athlete_data.get('lastname', ''),
                'email': athlete_data.get('email', ''),
                'city': athlete_data.get('city', ''),
                'state': athlete_data.get('state', ''),
                'country': athlete_data.get('country', ''),
                'sex': athlete_data.get('sex', ''),
                'premium': athlete_data.get('premium', False)
            }
        else:
            # Fallback to API call
            athlete_info = strava_service.get_athlete_info(token_data['access_token'])

        # Create or update athlete in simple database
        existing_athlete = db.get_athlete_by_strava_id(athlete_info['strava_id'])

        if existing_athlete:
            # Update existing athlete
            athlete = db.update_athlete(existing_athlete['id'], {
                **athlete_info,
                'last_sync': datetime.now().isoformat()
            })
        else:
            # Create new athlete
            athlete = db.create_athlete({
                **athlete_info,
                'last_sync': datetime.now().isoformat()
            })
        
        # Schedule background task to download 2025 activities
        background_tasks.add_task(
            download_activities_background,
            athlete['id'],
            token_data['access_token']
        )
        
        # Save database
        db.save_to_file()
        
        # Redirect to frontend with success
        frontend_url = "http://localhost:5173"
        return RedirectResponse(
            url=f"{frontend_url}?strava_connected=true&athlete_id={athlete['id']}",
            status_code=302
        )
        
    except Exception as e:
        logger.error(f"Strava callback error: {str(e)}")
        logger.error(f"Error type: {type(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")

        # Redirect to frontend with error
        frontend_url = "http://localhost:5173"
        error_message = str(e).replace(' ', '_').replace("'", "")[:100]  # URL safe
        return RedirectResponse(
            url=f"{frontend_url}?strava_error=true&message={error_message}",
            status_code=302
        )

@router.post("/sync-activities/{athlete_id}")
async def sync_activities(
    athlete_id: int,
    access_token: str,
    background_tasks: BackgroundTasks
):
    """
    Manually trigger activity sync for an athlete
    """
    db = get_db()

    # Verify athlete exists
    athlete = db.get_athlete(athlete_id)
    if not athlete:
        raise HTTPException(status_code=404, detail="Athlete not found")

    # Schedule background task
    background_tasks.add_task(
        download_activities_background,
        athlete_id,
        access_token
    )

    return {
        "message": "Activity sync started",
        "athlete_id": athlete_id,
        "status": "processing"
    }

@router.post("/resync/{athlete_id}")
async def resync_activities(
    athlete_id: int,
    background_tasks: BackgroundTasks
):
    """
    Trigger a fresh sync for an athlete (requires new Strava auth)
    """
    db = get_db()

    # Verify athlete exists
    athlete = db.get_athlete(athlete_id)
    if not athlete:
        raise HTTPException(status_code=404, detail="Athlete not found")

    return {
        "message": "Please reconnect to Strava to sync recent activities",
        "athlete_id": athlete_id,
        "instructions": "Click 'Connect Strava' again to refresh your activities",
        "note": "Updated to download last 6 months of activities instead of just 2025"
    }

@router.get("/sync-status/{athlete_id}")
async def get_sync_status(athlete_id: int):
    """
    Get sync status for an athlete
    """
    db = get_db()
    
    athlete = db.get_athlete(athlete_id)
    if not athlete:
        raise HTTPException(status_code=404, detail="Athlete not found")
    
    # Get activities for 2025
    activities_2025 = db.get_activities_2025(athlete_id)
    
    latest_activity = None
    if activities_2025:
        latest_dates = [a.get('start_date') for a in activities_2025 if a.get('start_date')]
        if latest_dates:
            latest_activity = max(latest_dates)
    
    return {
        "athlete_id": athlete_id,
        "athlete_name": f"{athlete.get('first_name', '')} {athlete.get('last_name', '')}".strip(),
        "last_sync": athlete.get('last_sync'),
        "activities_2025_count": len(activities_2025),
        "latest_activity": latest_activity,
        "sync_status": "completed" if athlete.get('last_sync') else "pending"
    }

@router.get("/activities/{athlete_id}")
async def get_athlete_activities(athlete_id: int):
    """
    Get all activities for an athlete
    """
    db = get_db()
    
    athlete = db.get_athlete(athlete_id)
    if not athlete:
        raise HTTPException(status_code=404, detail="Athlete not found")
    
    activities = db.get_activities_by_athlete(athlete_id)
    
    return {
        "athlete_id": athlete_id,
        "total_activities": len(activities),
        "activities": activities[:50]  # Return first 50 for display
    }

def download_activities_background(athlete_id: int, access_token: str):
    """
    Background task to download activities from Strava
    """
    strava_service = StravaService()
    db = get_db()

    try:
        logger.info(f"Starting activity download for athlete {athlete_id}")
        logger.info(f"Using access token: {access_token[:10]}...")

        # Download recent activities (last 6 months)
        activities_data = strava_service.get_activities_2025(access_token)

        logger.info(f"Downloaded {len(activities_data)} activities from Strava")

        if len(activities_data) == 0:
            logger.warning(f"No activities found for athlete {athlete_id}. This could mean:")
            logger.warning("1. No activities in the last 6 months")
            logger.warning("2. API permissions issue")
            logger.warning("3. Private activities")

        # Save activities to database
        saved_count = 0
        for activity_data in activities_data:
            activity_data['athlete_id'] = athlete_id
            logger.info(f"Processing activity: {activity_data.get('name', 'Unknown')} - {activity_data.get('start_date', 'No date')}")

            # Check if activity already exists
            existing_activity = db.get_activity_by_strava_id(activity_data['strava_id'])

            if not existing_activity:
                db.create_activity(activity_data)
                saved_count += 1
                logger.info(f"Saved new activity: {activity_data.get('name', 'Unknown')}")
            else:
                logger.info(f"Activity already exists: {activity_data.get('name', 'Unknown')}")

        # Update athlete last sync time
        db.update_athlete(athlete_id, {
            'last_sync': datetime.now().isoformat()
        })

        # Save database
        db.save_to_file()

        logger.info(f"Sync completed: {saved_count} new activities saved for athlete {athlete_id}")

    except Exception as e:
        logger.error(f"Error in background activity download: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")

@router.get("/test-connection")
async def test_strava_connection():
    """
    Test endpoint to verify Strava API configuration
    """
    strava_service = StravaService()
    
    return {
        "client_id_configured": bool(strava_service.client_id),
        "client_secret_configured": bool(strava_service.client_secret),
        "redirect_uri": strava_service.redirect_uri,
        "ready": bool(strava_service.client_id and strava_service.client_secret),
        "setup_instructions": {
            "1": "Go to https://developers.strava.com/",
            "2": "Create an application",
            "3": "Set Authorization Callback Domain to: localhost",
            "4": "Copy Client ID and Client Secret",
            "5": "Create .env file in backend directory",
            "6": "Add: STRAVA_CLIENT_ID=your_client_id",
            "7": "Add: STRAVA_CLIENT_SECRET=your_client_secret"
        }
    }

@router.get("/athletes")
async def list_athletes():
    """
    List all athletes in the database
    """
    db = get_db()

    athletes = []
    for athlete_id, athlete_data in db.athletes.items():
        activities_count = len(db.get_activities_by_athlete(athlete_id))
        activities_2025_count = len(db.get_activities_2025(athlete_id))

        athletes.append({
            "id": athlete_id,
            "name": f"{athlete_data.get('first_name', '')} {athlete_data.get('last_name', '')}".strip(),
            "strava_id": athlete_data.get('strava_id'),
            "last_sync": athlete_data.get('last_sync'),
            "total_activities": activities_count,
            "activities_2025": activities_2025_count
        })

    return {
        "athletes": athletes,
        "total_count": len(athletes)
    }

@router.get("/athlete/{athlete_id}")
async def get_athlete_profile(athlete_id: int):
    """
    Get athlete profile data
    """
    db = get_db()

    athlete = db.get_athlete(athlete_id)
    if not athlete:
        raise HTTPException(status_code=404, detail="Athlete not found")

    return athlete

@router.put("/athlete/{athlete_id}")
async def update_athlete_profile(athlete_id: int, updates: dict):
    """
    Update athlete profile data
    """
    db = get_db()

    athlete = db.get_athlete(athlete_id)
    if not athlete:
        raise HTTPException(status_code=404, detail="Athlete not found")

    # Update athlete data
    updated_athlete = db.update_athlete(athlete_id, updates)

    # Save to file
    db.save_to_file()

    return updated_athlete

@router.post("/calculate-hr-zones")
async def calculate_hr_zones(data: dict):
    """
    Calculate heart rate zones from lactate threshold
    """
    lactate_threshold_hr = data.get('lactate_threshold_hr')
    max_hr = data.get('max_hr')

    if not lactate_threshold_hr or not max_hr:
        raise HTTPException(status_code=400, detail="lactate_threshold_hr and max_hr are required")

    # Calculate zones using the formula from the training zones model
    zones = {
        1: {"min": 0, "max": int(lactate_threshold_hr * 0.85)},
        2: {"min": int(lactate_threshold_hr * 0.85), "max": int(lactate_threshold_hr * 0.89)},
        3: {"min": int(lactate_threshold_hr * 0.89), "max": int(lactate_threshold_hr * 0.94)},
        4: {"min": int(lactate_threshold_hr * 0.94), "max": int(lactate_threshold_hr * 1.05)},
        5: {"min": int(lactate_threshold_hr * 1.05), "max": max_hr},
    }

    return {
        "zones": zones,
        "zone_type": "heart_rate",
        "sport_type": "running"
    }

@router.post("/calculate-power-zones")
async def calculate_power_zones(ftp: float):
    """
    Calculate power zones from FTP
    """
    if not ftp:
        raise HTTPException(status_code=400, detail="FTP is required")

    # Calculate zones using the formula from the training zones model
    zones = {
        1: {"min": 0, "max": int(ftp * 0.55)},
        2: {"min": int(ftp * 0.55), "max": int(ftp * 0.75)},
        3: {"min": int(ftp * 0.75), "max": int(ftp * 0.90)},
        4: {"min": int(ftp * 0.90), "max": int(ftp * 1.05)},
        5: {"min": int(ftp * 1.05), "max": int(ftp * 1.50)},
    }

    return {
        "zones": zones,
        "zone_type": "power",
        "sport_type": "cycling",
        "ftp": ftp
    }

@router.get("/debug-auth")
async def debug_auth_url():
    """
    Debug endpoint to test auth URL generation
    """
    strava_service = StravaService()

    try:
        auth_url = strava_service.get_authorization_url()
        return {
            "success": True,
            "auth_url": auth_url,
            "client_id": strava_service.client_id,
            "redirect_uri": strava_service.redirect_uri
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "client_id_type": type(strava_service.client_id).__name__,
            "client_id_value": strava_service.client_id
        }
