"""
Simplified Strava endpoints using simple database
"""

from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from fastapi.responses import RedirectResponse
from typing import List, Dict
import os
from datetime import datetime, timedelta
import logging
import numpy as np

from app.core.simple_db import get_db
from app.services.strava_service import StravaService

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/auth-url")
async def get_strava_auth_url():
    """
    Get Strava authorization URL to start OAuth flow
    """
    strava_service = StravaService()
    
    if not strava_service.client_id:
        return {
            "error": "Strava API not configured",
            "instructions": "Please set STRAVA_CLIENT_ID and STRAVA_CLIENT_SECRET environment variables",
            "setup_guide": {
                "step_1": "Go to https://developers.strava.com/",
                "step_2": "Create an application",
                "step_3": "Copy Client ID and Client Secret",
                "step_4": "Set environment variables in .env file"
            }
        }
    
    auth_url = strava_service.get_authorization_url()
    
    return {
        "auth_url": auth_url,
        "instructions": "Visit this URL to authorize the application with your Strava account"
    }

@router.get("/callback")
async def strava_callback(
    code: str = Query(..., description="Authorization code from Strava"),
    scope: str = Query(None, description="Granted scopes"),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """
    Handle Strava OAuth callback and exchange code for token
    """
    strava_service = StravaService()
    db = get_db()
    
    try:
        # Exchange code for token
        token_data = strava_service.exchange_code_for_token(code)

        # Get athlete info - try from token first, then API call
        athlete_info = None
        if token_data.get('athlete_info'):
            # Use athlete info from token response
            athlete_data = token_data['athlete_info']
            athlete_info = {
                'strava_id': str(athlete_data.get('id', '')),
                'first_name': athlete_data.get('firstname', ''),
                'last_name': athlete_data.get('lastname', ''),
                'email': athlete_data.get('email', ''),
                'city': athlete_data.get('city', ''),
                'state': athlete_data.get('state', ''),
                'country': athlete_data.get('country', ''),
                'sex': athlete_data.get('sex', ''),
                'premium': athlete_data.get('premium', False)
            }
        else:
            # Fallback to API call
            athlete_info = strava_service.get_athlete_info(token_data['access_token'])

        # Create or update athlete in simple database
        existing_athlete = db.get_athlete_by_strava_id(athlete_info['strava_id'])

        if existing_athlete:
            # Update existing athlete
            athlete = db.update_athlete(existing_athlete['id'], {
                **athlete_info,
                'last_sync': datetime.now().isoformat()
            })
        else:
            # Create new athlete
            athlete = db.create_athlete({
                **athlete_info,
                'last_sync': datetime.now().isoformat()
            })
        
        # Schedule background task to download 2025 activities
        background_tasks.add_task(
            download_activities_background,
            athlete['id'],
            token_data['access_token']
        )
        
        # Save database
        db.save_to_file()
        
        # Redirect to frontend with success
        frontend_url = "http://localhost:5173"
        return RedirectResponse(
            url=f"{frontend_url}?strava_connected=true&athlete_id={athlete['id']}",
            status_code=302
        )
        
    except Exception as e:
        logger.error(f"Strava callback error: {str(e)}")
        logger.error(f"Error type: {type(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")

        # Redirect to frontend with error
        frontend_url = "http://localhost:5173"
        error_message = str(e).replace(' ', '_').replace("'", "")[:100]  # URL safe
        return RedirectResponse(
            url=f"{frontend_url}?strava_error=true&message={error_message}",
            status_code=302
        )

@router.post("/sync-activities/{athlete_id}")
async def sync_activities(
    athlete_id: int,
    access_token: str,
    background_tasks: BackgroundTasks
):
    """
    Manually trigger activity sync for an athlete
    """
    db = get_db()

    # Verify athlete exists
    athlete = db.get_athlete(athlete_id)
    if not athlete:
        raise HTTPException(status_code=404, detail="Athlete not found")

    # Schedule background task
    background_tasks.add_task(
        download_activities_background,
        athlete_id,
        access_token
    )

    return {
        "message": "Activity sync started",
        "athlete_id": athlete_id,
        "status": "processing"
    }

@router.post("/resync/{athlete_id}")
async def resync_activities(
    athlete_id: int,
    background_tasks: BackgroundTasks
):
    """
    Trigger a fresh sync for an athlete (requires new Strava auth)
    """
    db = get_db()

    # Verify athlete exists
    athlete = db.get_athlete(athlete_id)
    if not athlete:
        raise HTTPException(status_code=404, detail="Athlete not found")

    return {
        "message": "Please reconnect to Strava to sync recent activities",
        "athlete_id": athlete_id,
        "instructions": "Click 'Connect Strava' again to refresh your activities",
        "note": "Updated to download last 6 months of activities instead of just 2025"
    }

@router.get("/sync-status/{athlete_id}")
async def get_sync_status(athlete_id: int):
    """
    Get sync status for an athlete
    """
    db = get_db()
    
    athlete = db.get_athlete(athlete_id)
    if not athlete:
        raise HTTPException(status_code=404, detail="Athlete not found")
    
    # Get activities for 2025
    activities_2025 = db.get_activities_2025(athlete_id)
    
    latest_activity = None
    if activities_2025:
        latest_dates = [a.get('start_date') for a in activities_2025 if a.get('start_date')]
        if latest_dates:
            latest_activity = max(latest_dates)
    
    return {
        "athlete_id": athlete_id,
        "athlete_name": f"{athlete.get('first_name', '')} {athlete.get('last_name', '')}".strip(),
        "last_sync": athlete.get('last_sync'),
        "activities_2025_count": len(activities_2025),
        "latest_activity": latest_activity,
        "sync_status": "completed" if athlete.get('last_sync') else "pending"
    }

@router.get("/activities/{athlete_id}")
async def get_athlete_activities(athlete_id: int):
    """
    Get all activities for an athlete
    """
    db = get_db()
    
    athlete = db.get_athlete(athlete_id)
    if not athlete:
        raise HTTPException(status_code=404, detail="Athlete not found")
    
    activities = db.get_activities_by_athlete(athlete_id)
    
    return {
        "athlete_id": athlete_id,
        "total_activities": len(activities),
        "activities": activities[:50]  # Return first 50 for display
    }

def download_activities_background(athlete_id: int, access_token: str):
    """
    Background task to download activities from Strava
    """
    strava_service = StravaService()
    db = get_db()

    try:
        logger.info(f"Starting activity download for athlete {athlete_id}")
        logger.info(f"Using access token: {access_token[:10]}...")

        # Download recent activities (last 6 months)
        activities_data = strava_service.get_activities_2025(access_token)

        logger.info(f"Downloaded {len(activities_data)} activities from Strava")

        if len(activities_data) == 0:
            logger.warning(f"No activities found for athlete {athlete_id}. This could mean:")
            logger.warning("1. No activities in the last 6 months")
            logger.warning("2. API permissions issue")
            logger.warning("3. Private activities")

        # Save activities to database
        saved_count = 0
        for activity_data in activities_data:
            activity_data['athlete_id'] = athlete_id
            logger.info(f"Processing activity: {activity_data.get('name', 'Unknown')} - {activity_data.get('start_date', 'No date')}")

            # Check if activity already exists
            existing_activity = db.get_activity_by_strava_id(activity_data['strava_id'])

            if not existing_activity:
                db.create_activity(activity_data)
                saved_count += 1
                logger.info(f"Saved new activity: {activity_data.get('name', 'Unknown')}")
            else:
                logger.info(f"Activity already exists: {activity_data.get('name', 'Unknown')}")

        # Update athlete last sync time
        db.update_athlete(athlete_id, {
            'last_sync': datetime.now().isoformat()
        })

        # Save database
        db.save_to_file()

        logger.info(f"Sync completed: {saved_count} new activities saved for athlete {athlete_id}")

    except Exception as e:
        logger.error(f"Error in background activity download: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")

@router.get("/test-connection")
async def test_strava_connection():
    """
    Test endpoint to verify Strava API configuration
    """
    strava_service = StravaService()
    
    return {
        "client_id_configured": bool(strava_service.client_id),
        "client_secret_configured": bool(strava_service.client_secret),
        "redirect_uri": strava_service.redirect_uri,
        "ready": bool(strava_service.client_id and strava_service.client_secret),
        "setup_instructions": {
            "1": "Go to https://developers.strava.com/",
            "2": "Create an application",
            "3": "Set Authorization Callback Domain to: localhost",
            "4": "Copy Client ID and Client Secret",
            "5": "Create .env file in backend directory",
            "6": "Add: STRAVA_CLIENT_ID=your_client_id",
            "7": "Add: STRAVA_CLIENT_SECRET=your_client_secret"
        }
    }

@router.get("/athletes")
async def list_athletes():
    """
    List all athletes in the database
    """
    db = get_db()

    athletes = []
    for athlete_id, athlete_data in db.athletes.items():
        activities_count = len(db.get_activities_by_athlete(athlete_id))
        activities_2025_count = len(db.get_activities_2025(athlete_id))

        athletes.append({
            "id": athlete_id,
            "name": f"{athlete_data.get('first_name', '')} {athlete_data.get('last_name', '')}".strip(),
            "strava_id": athlete_data.get('strava_id'),
            "last_sync": athlete_data.get('last_sync'),
            "total_activities": activities_count,
            "activities_2025": activities_2025_count
        })

    return {
        "athletes": athletes,
        "total_count": len(athletes)
    }

@router.get("/athlete/{athlete_id}")
async def get_athlete_profile(athlete_id: int):
    """
    Get athlete profile data
    """
    db = get_db()

    athlete = db.get_athlete(athlete_id)
    if not athlete:
        raise HTTPException(status_code=404, detail="Athlete not found")

    return athlete

@router.put("/athlete/{athlete_id}")
async def update_athlete_profile(athlete_id: int, updates: dict):
    """
    Update athlete profile data
    """
    db = get_db()

    athlete = db.get_athlete(athlete_id)
    if not athlete:
        raise HTTPException(status_code=404, detail="Athlete not found")

    # Update athlete data
    updated_athlete = db.update_athlete(athlete_id, updates)

    # Save to file
    db.save_to_file()

    return updated_athlete

@router.post("/calculate-hr-zones")
async def calculate_hr_zones(data: dict):
    """
    Calculate heart rate zones from lactate threshold
    """
    lactate_threshold_hr = data.get('lactate_threshold_hr')
    max_hr = data.get('max_hr')

    if not lactate_threshold_hr or not max_hr:
        raise HTTPException(status_code=400, detail="lactate_threshold_hr and max_hr are required")

    # Calculate zones using the formula from the training zones model
    zones = {
        1: {"min": 0, "max": int(lactate_threshold_hr * 0.85)},
        2: {"min": int(lactate_threshold_hr * 0.85), "max": int(lactate_threshold_hr * 0.89)},
        3: {"min": int(lactate_threshold_hr * 0.89), "max": int(lactate_threshold_hr * 0.94)},
        4: {"min": int(lactate_threshold_hr * 0.94), "max": int(lactate_threshold_hr * 1.05)},
        5: {"min": int(lactate_threshold_hr * 1.05), "max": max_hr},
    }

    return {
        "zones": zones,
        "zone_type": "heart_rate",
        "sport_type": "running"
    }

@router.post("/calculate-power-zones")
async def calculate_power_zones(ftp: float):
    """
    Calculate power zones from FTP
    """
    if not ftp:
        raise HTTPException(status_code=400, detail="FTP is required")

    # Calculate zones using the formula from the training zones model
    zones = {
        1: {"min": 0, "max": int(ftp * 0.55)},
        2: {"min": int(ftp * 0.55), "max": int(ftp * 0.75)},
        3: {"min": int(ftp * 0.75), "max": int(ftp * 0.90)},
        4: {"min": int(ftp * 0.90), "max": int(ftp * 1.05)},
        5: {"min": int(ftp * 1.05), "max": int(ftp * 1.50)},
    }

    return {
        "zones": zones,
        "zone_type": "power",
        "sport_type": "cycling",
        "ftp": ftp
    }

@router.post("/manual-sync/{athlete_id}")
async def manual_sync_activities(athlete_id: int):
    """
    Manually trigger activity sync with detailed logging
    """
    db = get_db()

    # Verify athlete exists
    athlete = db.get_athlete(athlete_id)
    if not athlete:
        raise HTTPException(status_code=404, detail="Athlete not found")

    # For testing, we'll need a fresh access token
    # In a real app, you'd store and refresh tokens
    return {
        "message": "To manually sync activities, please reconnect your Strava account",
        "instructions": "Click 'Connect Strava' again to get a fresh access token and trigger sync",
        "athlete_id": athlete_id,
        "current_activities": len(db.get_activities_by_athlete(athlete_id))
    }

@router.get("/analytics/activity-summary/{athlete_id}")
async def get_activity_summary(athlete_id: int):
    """
    Get activity summary analytics for dashboard
    """
    db = get_db()

    activities = db.get_activities_by_athlete(athlete_id)

    if not activities:
        return {"error": "No activities found"}

    # Calculate summary statistics
    total_activities = len(activities)

    # Group by sport type
    sport_stats = {}
    monthly_stats = {}

    for activity in activities:
        sport = activity.get('sport_type', 'unknown')
        if sport not in sport_stats:
            sport_stats[sport] = {
                'count': 0,
                'total_distance': 0,
                'total_time': 0,
                'avg_hr': []
            }

        sport_stats[sport]['count'] += 1

        if activity.get('distance'):
            sport_stats[sport]['total_distance'] += activity['distance'] / 1000  # Convert to km

        if activity.get('moving_time'):
            sport_stats[sport]['total_time'] += activity['moving_time'] / 3600  # Convert to hours

        if activity.get('average_heartrate'):
            sport_stats[sport]['avg_hr'].append(activity['average_heartrate'])

        # Monthly grouping
        start_date = activity.get('start_date', '')
        if start_date:
            month_key = start_date[:7]  # YYYY-MM
            if month_key not in monthly_stats:
                monthly_stats[month_key] = {'count': 0, 'distance': 0}
            monthly_stats[month_key]['count'] += 1
            if activity.get('distance'):
                monthly_stats[month_key]['distance'] += activity['distance'] / 1000

    # Calculate averages
    for sport in sport_stats:
        if sport_stats[sport]['avg_hr']:
            sport_stats[sport]['avg_hr'] = sum(sport_stats[sport]['avg_hr']) / len(sport_stats[sport]['avg_hr'])
        else:
            sport_stats[sport]['avg_hr'] = None

    return {
        "total_activities": total_activities,
        "sport_breakdown": sport_stats,
        "monthly_trends": monthly_stats,
        "athlete_id": athlete_id
    }

@router.get("/analytics/periodization/{athlete_id}")
async def get_periodization_metrics(athlete_id: int):
    """
    Get advanced periodization metrics for professional training analysis
    """
    db = get_db()
    activities = db.get_activities_by_athlete(athlete_id)

    if not activities:
        return {"error": "No activities found"}

    # Get athlete profile for zone calculations
    athlete = db.get_athlete(athlete_id)
    lt_hr = athlete.get('lactate_threshold_hr') if athlete else None
    ftp = athlete.get('ftp') if athlete else None

    # Calculate Training Stress Balance (TSB)
    from datetime import datetime, timedelta
    import numpy as np

    # Sort activities by date
    sorted_activities = sorted(activities, key=lambda x: x.get('start_date', ''))

    # Calculate daily TSS
    daily_tss = {}
    for activity in sorted_activities:
        date_str = activity.get('start_date', '')[:10]  # YYYY-MM-DD
        tss = activity.get('training_stress_score', 0) or 0
        daily_tss[date_str] = daily_tss.get(date_str, 0) + tss

    # Calculate ATL (7-day) and CTL (42-day) rolling averages
    dates = sorted(daily_tss.keys())
    atl_data = []
    ctl_data = []
    tsb_data = []

    for i, date in enumerate(dates):
        # ATL: 7-day exponentially weighted average
        atl_window = dates[max(0, i-6):i+1]
        atl = np.mean([daily_tss.get(d, 0) for d in atl_window])

        # CTL: 42-day exponentially weighted average
        ctl_window = dates[max(0, i-41):i+1]
        ctl = np.mean([daily_tss.get(d, 0) for d in ctl_window])

        # TSB: Training Stress Balance
        tsb = ctl - atl

        atl_data.append({'date': date, 'value': atl})
        ctl_data.append({'date': date, 'value': ctl})
        tsb_data.append({'date': date, 'value': tsb})

    # Detect training phases
    phase_detection = detect_training_phase(daily_tss, dates)

    # Sport-specific TSS breakdown
    sport_tss = {}
    for activity in activities:
        sport = activity.get('sport_type', 'unknown').replace("root='", "").replace("'", "")
        tss = activity.get('training_stress_score', 0) or 0
        sport_tss[sport] = sport_tss.get(sport, 0) + tss

    return {
        "training_stress_balance": {
            "atl": atl_data[-7:],  # Last 7 days
            "ctl": ctl_data[-7:],  # Last 7 days
            "tsb": tsb_data[-7:],  # Last 7 days
            "current_tsb": tsb_data[-1]['value'] if tsb_data else 0
        },
        "phase_detection": phase_detection,
        "sport_tss_breakdown": sport_tss,
        "zone_configuration": {
            "lactate_threshold_hr": lt_hr,
            "ftp": ftp,
            "zones_configured": bool(lt_hr or ftp)
        }
    }

def detect_training_phase(daily_tss, dates):
    """Detect current training phase based on load patterns"""
    if len(dates) < 14:
        return {"phase": "insufficient_data", "confidence": 0}

    # Get recent 2 weeks vs previous 4 weeks
    recent_tss = [daily_tss.get(d, 0) for d in dates[-14:]]
    previous_tss = [daily_tss.get(d, 0) for d in dates[-42:-14]]

    recent_avg = np.mean(recent_tss)
    previous_avg = np.mean(previous_tss)

    volume_change = (recent_avg - previous_avg) / previous_avg if previous_avg > 0 else 0

    if volume_change < -0.4:  # 40% drop
        return {"phase": "taper", "confidence": 0.9, "volume_change": volume_change}
    elif volume_change > 0.1:  # 10% increase
        return {"phase": "build", "confidence": 0.8, "volume_change": volume_change}
    else:
        return {"phase": "maintenance", "confidence": 0.7, "volume_change": volume_change}

@router.get("/analytics/zone-analysis/{athlete_id}")
async def get_zone_analysis(athlete_id: int, sport_type: str = "run"):
    """
    Get zone distribution analysis for specific sport
    """
    db = get_db()
    activities = db.get_activities_by_athlete(athlete_id)
    athlete = db.get_athlete(athlete_id)

    if not activities or not athlete:
        return {"error": "No data found"}

    # Filter activities by sport
    sport_activities = [a for a in activities if sport_type in a.get('sport_type', '')]

    if not sport_activities:
        return {"error": f"No {sport_type} activities found"}

    # Get zone thresholds from athlete profile
    lt_hr = athlete.get('lactate_threshold_hr')
    max_hr = athlete.get('max_hr')
    ftp = athlete.get('ftp')

    if not lt_hr and sport_type == 'run':
        return {"error": "Lactate threshold HR not configured in Athlete Profile"}
    if not ftp and sport_type == 'ride':
        return {"error": "FTP not configured in Athlete Profile"}

    # Calculate zones based on sport using athlete's actual values
    if sport_type == 'run' and lt_hr:
        # Use lactate threshold-based zones (professional coaching standard)
        zones = {
            'Z1': (50, int(0.85 * lt_hr)),           # Recovery: 50 - 85% of LT
            'Z2': (int(0.85 * lt_hr) + 1, int(0.89 * lt_hr)),  # Aerobic: 85-89% of LT
            'Z3': (int(0.89 * lt_hr) + 1, int(0.94 * lt_hr)),  # Tempo: 89-94% of LT
            'Z4': (int(0.94 * lt_hr) + 1, int(1.05 * lt_hr)),  # Threshold: 94-105% of LT
            'Z5': (int(1.05 * lt_hr) + 1, max_hr if max_hr else int(1.15 * lt_hr))  # VO2Max: 105%+ of LT
        }
        metric = 'average_heartrate'
        zone_type = 'Heart Rate'
        zone_unit = 'bpm'
    elif sport_type == 'ride' and ftp:
        # Use FTP-based power zones (professional cycling standard)
        zones = {
            'Z1': (0, int(0.55 * ftp)),              # Active Recovery: 0-55% FTP
            'Z2': (int(0.55 * ftp) + 1, int(0.75 * ftp)),    # Endurance: 56-75% FTP
            'Z3': (int(0.75 * ftp) + 1, int(0.90 * ftp)),    # Tempo: 76-90% FTP
            'Z4': (int(0.90 * ftp) + 1, int(1.05 * ftp)),    # Threshold: 91-105% FTP
            'Z5': (int(1.05 * ftp) + 1, int(1.20 * ftp))     # VO2Max: 106-120% FTP
        }
        metric = 'average_watts'
        zone_type = 'Power'
        zone_unit = 'W'
    else:
        return {"error": "Zone configuration incomplete - check Athlete Profile"}

    # Analyze zone distribution
    zone_time = {zone: 0 for zone in zones.keys()}
    zone_activities = {zone: [] for zone in zones.keys()}

    for activity in sport_activities:
        value = activity.get(metric)
        moving_time = activity.get('moving_time', 0)

        if value and moving_time:
            # Determine which zone this activity falls into
            for zone, (min_val, max_val) in zones.items():
                if min_val <= value <= max_val:
                    zone_time[zone] += moving_time
                    zone_activities[zone].append({
                        'name': activity.get('name'),
                        'date': activity.get('start_date'),
                        'value': value,
                        'time': moving_time
                    })
                    break

    # Calculate percentages
    total_time = sum(zone_time.values())
    zone_percentages = {zone: (time/total_time)*100 if total_time > 0 else 0
                       for zone, time in zone_time.items()}

    return {
        "sport_type": sport_type,
        "zone_distribution": zone_percentages,
        "zone_time_seconds": zone_time,
        "zone_activities": zone_activities,
        "total_time_hours": total_time / 3600,
        "zones_config": zones,
        "metric_used": metric,
        "zone_type": zone_type,
        "zone_unit": zone_unit,
        "athlete_config": {
            "lactate_threshold_hr": lt_hr,
            "max_hr": max_hr,
            "ftp": ftp
        },
        "zone_definitions": {
            "Z1": "Recovery/Active Recovery",
            "Z2": "Aerobic/Endurance",
            "Z3": "Tempo",
            "Z4": "Lactate Threshold",
            "Z5": "VO2Max/Neuromuscular Power"
        }
    }

@router.get("/analytics/activity-chart-data/{athlete_id}")
async def get_activity_chart_data(athlete_id: int):
    """
    Get data formatted for Plotly charts
    """
    db = get_db()

    activities = db.get_activities_by_athlete(athlete_id)

    if not activities:
        return {"error": "No activities found"}

    # Prepare data for different chart types

    # 1. Activities by sport type (pie chart)
    sport_counts = {}
    for activity in activities:
        sport = activity.get('sport_type', 'unknown').replace("root='", "").replace("'", "")
        sport_counts[sport] = sport_counts.get(sport, 0) + 1

    # 2. Monthly activity trend (line chart)
    monthly_data = {}
    for activity in activities:
        start_date = activity.get('start_date', '')
        if start_date:
            month_key = start_date[:7]  # YYYY-MM
            monthly_data[month_key] = monthly_data.get(month_key, 0) + 1

    # Sort monthly data
    sorted_months = sorted(monthly_data.items())

    # 3. Distance vs Time scatter plot (for runs/rides)
    scatter_data = []
    for activity in activities:
        if activity.get('distance') and activity.get('moving_time'):
            sport = activity.get('sport_type', 'unknown').replace("root='", "").replace("'", "")
            if sport in ['run', 'ride']:
                scatter_data.append({
                    'distance_km': activity['distance'] / 1000,
                    'time_hours': activity['moving_time'] / 3600,
                    'sport': sport,
                    'name': activity.get('name', 'Unknown'),
                    'avg_hr': activity.get('average_heartrate')
                })

    # 4. Heart rate distribution
    hr_data = []
    for activity in activities:
        if activity.get('average_heartrate'):
            sport = activity.get('sport_type', 'unknown').replace("root='", "").replace("'", "")
            hr_data.append({
                'hr': activity['average_heartrate'],
                'sport': sport
            })

    return {
        "sport_distribution": {
            "labels": list(sport_counts.keys()),
            "values": list(sport_counts.values())
        },
        "monthly_trend": {
            "months": [item[0] for item in sorted_months],
            "counts": [item[1] for item in sorted_months]
        },
        "distance_time_scatter": scatter_data,
        "heart_rate_data": hr_data
    }

@router.get("/analytics/zone-trends/{athlete_id}")
async def get_zone_trends(athlete_id: int, sport_type: str = "run"):
    """
    Get weekly/monthly zone distribution trends for periodization analysis
    """
    db = get_db()
    activities = db.get_activities_by_athlete(athlete_id)
    athlete = db.get_athlete(athlete_id)

    if not activities or not athlete:
        return {"error": "No data found"}

    # Filter activities by sport
    sport_activities = [a for a in activities if sport_type in a.get('sport_type', '')]

    if not sport_activities:
        return {"error": f"No {sport_type} activities found"}

    # Get zone thresholds from athlete profile
    lt_hr = athlete.get('lactate_threshold_hr')
    max_hr = athlete.get('max_hr')
    ftp = athlete.get('ftp')

    if not lt_hr and sport_type == 'run':
        return {"error": "Lactate threshold HR not configured in Athlete Profile"}
    if not ftp and sport_type == 'ride':
        return {"error": "FTP not configured in Athlete Profile"}

    # Calculate zones using athlete's actual values (same as zone analysis)
    if sport_type == 'run' and lt_hr:
        zones = {
            'Z1': (50, int(0.85 * lt_hr)),
            'Z2': (int(0.85 * lt_hr) + 1, int(0.89 * lt_hr)),
            'Z3': (int(0.89 * lt_hr) + 1, int(0.94 * lt_hr)),
            'Z4': (int(0.94 * lt_hr) + 1, int(1.05 * lt_hr)),
            'Z5': (int(1.05 * lt_hr) + 1, max_hr if max_hr else int(1.15 * lt_hr))
        }
        metric = 'average_heartrate'
    elif sport_type == 'ride' and ftp:
        zones = {
            'Z1': (0, int(0.55 * ftp)),
            'Z2': (int(0.55 * ftp) + 1, int(0.75 * ftp)),
            'Z3': (int(0.75 * ftp) + 1, int(0.90 * ftp)),
            'Z4': (int(0.90 * ftp) + 1, int(1.05 * ftp)),
            'Z5': (int(1.05 * ftp) + 1, int(1.20 * ftp))
        }
        metric = 'average_watts'
    else:
        return {"error": "Zone configuration incomplete - check Athlete Profile"}

    # Group activities by week/month
    weekly_zones = {}
    monthly_zones = {}

    for activity in sport_activities:
        start_date = activity.get('start_date', '')
        if not start_date:
            continue

        # Get week and month keys
        date_obj = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        week_key = f"{date_obj.year}-W{date_obj.isocalendar()[1]:02d}"
        month_key = f"{date_obj.year}-{date_obj.month:02d}"

        # Initialize if not exists
        if week_key not in weekly_zones:
            weekly_zones[week_key] = {zone: 0 for zone in zones.keys()}
        if month_key not in monthly_zones:
            monthly_zones[month_key] = {zone: 0 for zone in zones.keys()}

        # Categorize activity by HR zone
        avg_hr = activity.get('average_heartrate')
        moving_time = activity.get('moving_time', 0)

        if avg_hr and moving_time:
            for zone, (min_hr, max_hr) in zones.items():
                if min_hr <= avg_hr <= max_hr:
                    weekly_zones[week_key][zone] += moving_time
                    monthly_zones[month_key][zone] += moving_time
                    break

    # Calculate percentages and trends
    weekly_trends = []
    for week, zone_times in sorted(weekly_zones.items()):
        total_time = sum(zone_times.values())
        if total_time > 0:
            percentages = {zone: (time/total_time)*100 for zone, time in zone_times.items()}
            weekly_trends.append({
                'period': week,
                'zone_percentages': percentages,
                'total_time_hours': total_time / 3600,
                'polarization_index': (percentages['Z1'] + percentages['Z2']) / (percentages['Z4'] + percentages['Z5']) if (percentages['Z4'] + percentages['Z5']) > 0 else 0
            })

    monthly_trends = []
    for month, zone_times in sorted(monthly_zones.items()):
        total_time = sum(zone_times.values())
        if total_time > 0:
            percentages = {zone: (time/total_time)*100 for zone, time in zone_times.items()}
            monthly_trends.append({
                'period': month,
                'zone_percentages': percentages,
                'total_time_hours': total_time / 3600,
                'polarization_index': (percentages['Z1'] + percentages['Z2']) / (percentages['Z4'] + percentages['Z5']) if (percentages['Z4'] + percentages['Z5']) > 0 else 0
            })

    return {
        "sport_type": sport_type,
        "weekly_trends": weekly_trends[-12:],  # Last 12 weeks
        "monthly_trends": monthly_trends[-6:],  # Last 6 months
        "zones_config": zones
    }

@router.get("/analytics/zone-recommendations/{athlete_id}")
async def get_zone_recommendations(athlete_id: int, sport_type: str = "run"):
    """
    Get personalized zone target recommendations based on training phase and goals
    """
    db = get_db()
    activities = db.get_activities_by_athlete(athlete_id)
    athlete = db.get_athlete(athlete_id)

    if not activities or not athlete:
        return {"error": "No data found"}

    # Get current zone distribution
    zone_analysis_response = await get_zone_analysis(athlete_id, sport_type)
    if 'error' in zone_analysis_response:
        return zone_analysis_response

    current_zones = zone_analysis_response['zone_distribution']

    # Define optimal zone distributions for different training phases
    phase_targets = {
        "base_building": {
            "Z1": 50, "Z2": 35, "Z3": 10, "Z4": 5, "Z5": 0,
            "description": "Aerobic base development phase",
            "focus": "Build aerobic capacity and fat oxidation"
        },
        "build": {
            "Z1": 40, "Z2": 30, "Z3": 15, "Z4": 12, "Z5": 3,
            "description": "Progressive intensity building",
            "focus": "Develop lactate threshold and VO2max"
        },
        "peak": {
            "Z1": 35, "Z2": 25, "Z3": 20, "Z4": 15, "Z5": 5,
            "description": "Race-specific intensity work",
            "focus": "Sharpen race pace and neuromuscular power"
        },
        "recovery": {
            "Z1": 70, "Z2": 25, "Z3": 5, "Z4": 0, "Z5": 0,
            "description": "Active recovery and regeneration",
            "focus": "Maintain fitness while recovering"
        }
    }

    # Determine current training phase based on zone distribution
    z1_z2_percentage = current_zones.get('Z1', 0) + current_zones.get('Z2', 0)
    z4_z5_percentage = current_zones.get('Z4', 0) + current_zones.get('Z5', 0)

    if z1_z2_percentage > 80:
        current_phase = "base_building"
    elif z4_z5_percentage > 15:
        current_phase = "peak"
    elif z4_z5_percentage > 8:
        current_phase = "build"
    else:
        current_phase = "base_building"

    # Generate recommendations
    target_zones = phase_targets[current_phase]
    recommendations = []

    for zone, target_pct in target_zones.items():
        if zone in ['description', 'focus']:
            continue

        current_pct = current_zones.get(zone, 0)
        difference = target_pct - current_pct

        if abs(difference) > 5:  # Only recommend if >5% difference
            if difference > 0:
                recommendations.append({
                    "zone": zone,
                    "action": "increase",
                    "current": current_pct,
                    "target": target_pct,
                    "difference": difference,
                    "suggestion": f"Add more {zone} training (+{difference:.1f}%)"
                })
            else:
                recommendations.append({
                    "zone": zone,
                    "action": "decrease",
                    "current": current_pct,
                    "target": target_pct,
                    "difference": abs(difference),
                    "suggestion": f"Reduce {zone} training (-{abs(difference):.1f}%)"
                })

    # Training phase recommendations
    next_phase_suggestions = {
        "base_building": "Consider adding tempo (Z3) work after 4-6 weeks",
        "build": "Progress to race-specific intensities (Z4-Z5)",
        "peak": "Maintain sharpness with quality over quantity",
        "recovery": "Focus on easy aerobic work and cross-training"
    }

    return {
        "current_phase": current_phase,
        "phase_description": target_zones['description'],
        "phase_focus": target_zones['focus'],
        "target_distribution": {k: v for k, v in target_zones.items() if k not in ['description', 'focus']},
        "current_distribution": current_zones,
        "recommendations": recommendations,
        "next_phase_suggestion": next_phase_suggestions.get(current_phase),
        "polarization_ratio": z1_z2_percentage / z4_z5_percentage if z4_z5_percentage > 0 else "∞",
        "training_stress_balance": "optimal" if 3 <= (z1_z2_percentage / z4_z5_percentage if z4_z5_percentage > 0 else 10) <= 5 else "review_needed"
    }

@router.get("/debug-strava-activity")
async def debug_strava_activity():
    """
    Debug endpoint to inspect Strava activity structure
    """
    from stravalib import Client
    import os

    # This is just for debugging - in real use we'd get this from the callback
    # For now, let's just return info about what we expect
    return {
        "message": "To debug activities, reconnect Strava and check server logs",
        "expected_duration_attributes": ["total_seconds", "seconds", "days", "microseconds"],
        "note": "Check server logs during Strava connection for detailed activity structure"
    }

@router.get("/test-activity-download/{athlete_id}")
async def test_activity_download(athlete_id: int, access_token: str):
    """
    Test endpoint to manually download activities with detailed logging
    """
    strava_service = StravaService()
    db = get_db()

    try:
        logger.info(f"Testing activity download for athlete {athlete_id}")

        # Test getting activities
        activities_data = strava_service.get_activities_2025(access_token)

        return {
            "success": True,
            "activities_found": len(activities_data),
            "activities_preview": activities_data[:3] if activities_data else [],
            "message": f"Found {len(activities_data)} activities"
        }

    except Exception as e:
        logger.error(f"Test download error: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")

        return {
            "success": False,
            "error": str(e),
            "message": "Activity download test failed"
        }

@router.get("/debug-auth")
async def debug_auth_url():
    """
    Debug endpoint to test auth URL generation
    """
    strava_service = StravaService()

    try:
        auth_url = strava_service.get_authorization_url()
        return {
            "success": True,
            "auth_url": auth_url,
            "client_id": strava_service.client_id,
            "redirect_uri": strava_service.redirect_uri
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "client_id_type": type(strava_service.client_id).__name__,
            "client_id_value": strava_service.client_id
        }
