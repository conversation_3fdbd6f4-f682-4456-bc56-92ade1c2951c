"""
Strava authentication and data sync endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from fastapi.responses import RedirectResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict
import os
from datetime import datetime
import logging

from app.core.database import get_db
from app.services.strava_service import StravaService
from app.models.athlete import Athlete
from app.models.activity import Activity

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/auth-url")
async def get_strava_auth_url():
    """
    Get Strava authorization URL to start OAuth flow
    """
    strava_service = StravaService()
    
    if not strava_service.client_id:
        raise HTTPException(
            status_code=500, 
            detail="Strava API not configured. Please set STRAVA_CLIENT_ID and STRAVA_CLIENT_SECRET environment variables."
        )
    
    auth_url = strava_service.get_authorization_url()
    
    return {
        "auth_url": auth_url,
        "instructions": "Visit this URL to authorize the application with your Strava account"
    }

@router.get("/callback")
async def strava_callback(
    code: str = Query(..., description="Authorization code from Strava"),
    scope: str = Query(None, description="Granted scopes"),
    db: AsyncSession = Depends(get_db),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """
    Handle Strava OAuth callback and exchange code for token
    """
    strava_service = StravaService()
    
    try:
        # Exchange code for token
        token_data = strava_service.exchange_code_for_token(code)
        
        # Get athlete info
        athlete_info = strava_service.get_athlete_info(token_data['access_token'])
        
        # Create or update athlete in database
        athlete = await create_or_update_athlete(db, athlete_info, token_data)
        
        # Schedule background task to download 2025 activities
        background_tasks.add_task(
            download_activities_background,
            athlete.id,
            token_data['access_token'],
            db
        )
        
        # Redirect to frontend with success
        frontend_url = "http://localhost:5173"
        return RedirectResponse(
            url=f"{frontend_url}?strava_connected=true&athlete_id={athlete.id}",
            status_code=302
        )
        
    except Exception as e:
        logger.error(f"Strava callback error: {str(e)}")
        # Redirect to frontend with error
        frontend_url = "http://localhost:5173"
        return RedirectResponse(
            url=f"{frontend_url}?strava_error=true&message={str(e)}",
            status_code=302
        )

@router.post("/sync-activities/{athlete_id}")
async def sync_activities(
    athlete_id: int,
    access_token: str,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """
    Manually trigger activity sync for an athlete
    """
    # Verify athlete exists
    athlete = await db.get(Athlete, athlete_id)
    if not athlete:
        raise HTTPException(status_code=404, detail="Athlete not found")
    
    # Schedule background task
    background_tasks.add_task(
        download_activities_background,
        athlete_id,
        access_token,
        db
    )
    
    return {
        "message": "Activity sync started",
        "athlete_id": athlete_id,
        "status": "processing"
    }

@router.get("/sync-status/{athlete_id}")
async def get_sync_status(
    athlete_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    Get sync status for an athlete
    """
    athlete = await db.get(Athlete, athlete_id)
    if not athlete:
        raise HTTPException(status_code=404, detail="Athlete not found")
    
    # Count activities for 2025
    from sqlalchemy import select, and_, extract
    
    query = select(Activity).where(
        and_(
            Activity.athlete_id == athlete_id,
            extract('year', Activity.start_date) == 2025
        )
    )
    
    result = await db.execute(query)
    activities_2025 = result.scalars().all()
    
    return {
        "athlete_id": athlete_id,
        "last_sync": athlete.last_sync,
        "activities_2025_count": len(activities_2025),
        "latest_activity": max([a.start_date for a in activities_2025]) if activities_2025 else None,
        "sync_status": "completed" if athlete.last_sync else "pending"
    }

async def create_or_update_athlete(
    db: AsyncSession, 
    athlete_info: Dict, 
    token_data: Dict
) -> Athlete:
    """
    Create or update athlete in database
    """
    from sqlalchemy import select
    
    # Check if athlete exists
    query = select(Athlete).where(Athlete.strava_id == athlete_info['strava_id'])
    result = await db.execute(query)
    athlete = result.scalar_one_or_none()
    
    if athlete:
        # Update existing athlete
        for key, value in athlete_info.items():
            if hasattr(athlete, key) and value is not None:
                setattr(athlete, key, value)
        athlete.last_sync = datetime.now()
    else:
        # Create new athlete
        athlete = Athlete(
            **athlete_info,
            last_sync=datetime.now()
        )
        db.add(athlete)
    
    await db.commit()
    await db.refresh(athlete)
    
    return athlete

async def download_activities_background(
    athlete_id: int,
    access_token: str,
    db: AsyncSession
):
    """
    Background task to download activities from Strava
    """
    strava_service = StravaService()
    
    try:
        logger.info(f"Starting activity download for athlete {athlete_id}")
        
        # Download 2025 activities
        activities_data = strava_service.get_activities_2025(access_token)
        
        logger.info(f"Downloaded {len(activities_data)} activities from Strava")
        
        # Save activities to database
        saved_count = 0
        for activity_data in activities_data:
            activity_data['athlete_id'] = athlete_id
            
            # Check if activity already exists
            from sqlalchemy import select
            query = select(Activity).where(
                Activity.strava_id == activity_data['strava_id']
            )
            result = await db.execute(query)
            existing_activity = result.scalar_one_or_none()
            
            if not existing_activity:
                activity = Activity(**activity_data)
                db.add(activity)
                saved_count += 1
        
        # Update athlete last sync time
        athlete = await db.get(Athlete, athlete_id)
        if athlete:
            athlete.last_sync = datetime.now()
        
        await db.commit()
        
        logger.info(f"Saved {saved_count} new activities for athlete {athlete_id}")
        
    except Exception as e:
        logger.error(f"Error in background activity download: {str(e)}")
        await db.rollback()

@router.get("/test-connection")
async def test_strava_connection():
    """
    Test endpoint to verify Strava API configuration
    """
    strava_service = StravaService()
    
    return {
        "client_id_configured": bool(strava_service.client_id),
        "client_secret_configured": bool(strava_service.client_secret),
        "redirect_uri": strava_service.redirect_uri,
        "ready": bool(strava_service.client_id and strava_service.client_secret)
    }
