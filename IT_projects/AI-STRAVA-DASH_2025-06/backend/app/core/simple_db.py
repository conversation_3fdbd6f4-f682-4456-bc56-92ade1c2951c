"""
Simple in-memory database for testing without SQLAlchemy complexity
"""

from typing import Dict, List, Optional
from datetime import datetime
import json

class SimpleDB:
    """Simple in-memory database for testing"""
    
    def __init__(self):
        self.athletes: Dict[int, Dict] = {}
        self.activities: Dict[int, Dict] = {}
        self.next_athlete_id = 1
        self.next_activity_id = 1
    
    def create_athlete(self, athlete_data: Dict) -> Dict:
        """Create a new athlete"""
        athlete = {
            'id': self.next_athlete_id,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            **athlete_data
        }
        
        self.athletes[self.next_athlete_id] = athlete
        self.next_athlete_id += 1
        
        return athlete
    
    def get_athlete(self, athlete_id: int) -> Optional[Dict]:
        """Get athlete by ID"""
        return self.athletes.get(athlete_id)
    
    def get_athlete_by_strava_id(self, strava_id: str) -> Optional[Dict]:
        """Get athlete by Strava ID"""
        for athlete in self.athletes.values():
            if athlete.get('strava_id') == strava_id:
                return athlete
        return None
    
    def update_athlete(self, athlete_id: int, updates: Dict) -> Optional[Dict]:
        """Update athlete data"""
        if athlete_id in self.athletes:
            self.athletes[athlete_id].update(updates)
            self.athletes[athlete_id]['updated_at'] = datetime.now().isoformat()
            return self.athletes[athlete_id]
        return None
    
    def create_activity(self, activity_data: Dict) -> Dict:
        """Create a new activity"""
        activity = {
            'id': self.next_activity_id,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            **activity_data
        }
        
        self.activities[self.next_activity_id] = activity
        self.next_activity_id += 1
        
        return activity
    
    def get_activities_by_athlete(self, athlete_id: int) -> List[Dict]:
        """Get all activities for an athlete"""
        return [
            activity for activity in self.activities.values()
            if activity.get('athlete_id') == athlete_id
        ]
    
    def get_activity_by_strava_id(self, strava_id: str) -> Optional[Dict]:
        """Get activity by Strava ID"""
        for activity in self.activities.values():
            if activity.get('strava_id') == strava_id:
                return activity
        return None
    
    def get_activities_2025(self, athlete_id: int) -> List[Dict]:
        """Get all activities for an athlete (renamed for compatibility)"""
        return self.get_activities_by_athlete(athlete_id)
    
    def save_to_file(self, filename: str = "fitness_data.json"):
        """Save data to JSON file"""
        data = {
            'athletes': self.athletes,
            'activities': self.activities,
            'next_athlete_id': self.next_athlete_id,
            'next_activity_id': self.next_activity_id
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2, default=str)
    
    def load_from_file(self, filename: str = "fitness_data.json"):
        """Load data from JSON file"""
        try:
            with open(filename, 'r') as f:
                data = json.load(f)
            
            self.athletes = {int(k): v for k, v in data.get('athletes', {}).items()}
            self.activities = {int(k): v for k, v in data.get('activities', {}).items()}
            self.next_athlete_id = data.get('next_athlete_id', 1)
            self.next_activity_id = data.get('next_activity_id', 1)
            
        except FileNotFoundError:
            pass  # File doesn't exist yet, start with empty database

# Global database instance
db = SimpleDB()

# Try to load existing data
db.load_from_file()

def get_db():
    """Get database instance"""
    return db
