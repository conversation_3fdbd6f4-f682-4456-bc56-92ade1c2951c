"""
Strava API integration service for downloading activities
"""

import os
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from stravalib import Client
import logging

logger = logging.getLogger(__name__)

class StravaService:
    """Service for interacting with Strava API"""
    
    def __init__(self):
        client_id_str = os.getenv('STRAVA_CLIENT_ID')
        self.client_id = int(client_id_str) if client_id_str and client_id_str.isdigit() else None
        self.client_secret = os.getenv('STRAVA_CLIENT_SECRET')
        self.redirect_uri = os.getenv('STRAVA_REDIRECT_URI', 'http://localhost:8000/api/v1/auth/strava/callback')

        if not self.client_id or not self.client_secret:
            logger.warning("Strava credentials not configured. Set STRAVA_CLIENT_ID and STRAVA_CLIENT_SECRET environment variables.")
    
    def get_authorization_url(self, scope: List[str] = None) -> str:
        """
        Get Strava authorization URL for OAuth flow
        """
        if scope is None:
            scope = ["read", "activity:read_all"]

        client = Client()
        auth_url = client.authorization_url(
            client_id=self.client_id,
            redirect_uri=self.redirect_uri,
            scope=scope
        )
        return auth_url
    
    def exchange_code_for_token(self, authorization_code: str) -> Dict:
        """
        Exchange authorization code for access token
        """
        client = Client()
        token_response = client.exchange_code_for_token(
            client_id=self.client_id,
            client_secret=self.client_secret,
            code=authorization_code
        )

        # Handle different response formats
        athlete_info = None
        athlete_id = None

        if 'athlete' in token_response:
            athlete_info = token_response['athlete']
            athlete_id = athlete_info.get('id') if athlete_info else None

        return {
            'access_token': token_response['access_token'],
            'refresh_token': token_response.get('refresh_token'),
            'expires_at': token_response.get('expires_at'),
            'athlete_id': athlete_id,
            'athlete_info': athlete_info
        }
    
    def get_activities_2025(self, access_token: str) -> List[Dict]:
        """
        Download recent activities for testing (last 6 months)
        """
        client = Client(access_token=access_token)

        # Define date range - last 6 months to current date
        end_date = datetime.now()
        start_date = end_date - timedelta(days=180)  # Last 6 months
        
        activities = []
        
        try:
            logger.info(f"Requesting activities from {start_date} to {end_date}")

            # Get activities from Strava
            strava_activities = client.get_activities(
                before=end_date,
                after=start_date,
                limit=200  # Adjust as needed
            )

            # Convert to list to see how many we got
            activity_list = list(strava_activities)
            logger.info(f"Received {len(activity_list)} activities from Strava API")

            for i, activity in enumerate(activity_list):
                try:
                    logger.info(f"Processing activity {i+1}/{len(activity_list)}: {activity.name} ({activity.start_date})")
                    activity_data = self._convert_strava_activity(activity)
                    activities.append(activity_data)
                    logger.info(f"Successfully converted activity: {activity.name}")
                except Exception as e:
                    logger.error(f"Error converting activity {activity.name}: {str(e)}")
                    continue

            logger.info(f"Successfully processed {len(activities)} activities")

        except Exception as e:
            logger.error(f"Error downloading activities: {str(e)}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            raise
        
        return activities
    
    def get_activity_details(self, access_token: str, activity_id: int) -> Dict:
        """
        Get detailed activity data including streams
        """
        client = Client(access_token=access_token)
        
        try:
            # Get detailed activity
            activity = client.get_activity(activity_id)
            
            # Get activity streams (time series data)
            streams = client.get_activity_streams(
                activity_id,
                types=['time', 'heartrate', 'watts', 'cadence', 'velocity_smooth', 'altitude'],
                resolution='medium'
            )
            
            activity_data = self._convert_strava_activity(activity)
            
            # Add stream data
            if streams:
                time_series = {}
                for stream_type, stream in streams.items():
                    time_series[stream_type] = stream.data
                
                activity_data['time_series_data'] = time_series
            
            return activity_data
            
        except Exception as e:
            logger.error(f"Error getting activity details for {activity_id}: {str(e)}")
            raise
    
    def _convert_strava_activity(self, activity) -> Dict:
        """
        Convert Strava activity to our database format
        """
        # Safely handle activity type
        sport_type = 'unknown'
        if activity.type:
            try:
                sport_type = str(activity.type).lower()
            except:
                sport_type = 'unknown'

        return {
            'strava_id': str(activity.id),
            'name': activity.name or 'Unnamed Activity',
            'sport_type': sport_type,
            'start_date': activity.start_date,
            'elapsed_time': self._safe_duration_to_seconds(activity.elapsed_time),
            'moving_time': self._safe_duration_to_seconds(activity.moving_time),
            'distance': float(activity.distance) if activity.distance else None,
            'average_speed': float(activity.average_speed) if activity.average_speed else None,
            'max_speed': float(activity.max_speed) if activity.max_speed else None,
            'average_heartrate': float(activity.average_heartrate) if activity.average_heartrate else None,
            'max_heartrate': float(activity.max_heartrate) if activity.max_heartrate else None,
            'average_watts': float(activity.average_watts) if activity.average_watts else None,
            'max_watts': float(activity.max_watts) if activity.max_watts else None,
            'total_elevation_gain': float(activity.total_elevation_gain) if activity.total_elevation_gain else None,
            'training_stress_score': float(activity.suffer_score) if activity.suffer_score else None,
            'is_race': activity.workout_type == 1 if activity.workout_type else False,
            'is_workout': activity.workout_type is not None,
            'workout_type': self._map_workout_type(activity.workout_type) if activity.workout_type else None,
            # Calculate additional metrics
            'average_pace': self._calculate_pace(activity.distance, activity.moving_time) if activity.distance and activity.moving_time else None,
        }
    
    def _safe_duration_to_seconds(self, duration) -> int:
        """Safely convert duration to seconds"""
        if not duration:
            return None

        try:
            # Debug logging
            logger.info(f"Converting duration: {duration} (type: {type(duration)})")
            logger.info(f"Duration attributes: {dir(duration)}")

            # Try different methods to get seconds
            if hasattr(duration, 'total_seconds'):
                result = int(duration.total_seconds())
                logger.info(f"Used total_seconds(): {result}")
                return result
            elif hasattr(duration, 'seconds'):
                result = int(duration.seconds)
                logger.info(f"Used seconds: {result}")
                return result
            elif isinstance(duration, (int, float)):
                result = int(duration)
                logger.info(f"Used direct conversion: {result}")
                return result
            else:
                # Try to convert to string and parse
                duration_str = str(duration)
                logger.info(f"Duration as string: '{duration_str}'")

                # Handle format like "0:45:30" (hours:minutes:seconds)
                if ':' in duration_str:
                    parts = duration_str.split(':')
                    if len(parts) == 3:
                        hours, minutes, seconds = map(int, parts)
                        result = hours * 3600 + minutes * 60 + seconds
                        logger.info(f"Parsed H:M:S format: {result}")
                        return result
                    elif len(parts) == 2:
                        minutes, seconds = map(int, parts)
                        result = minutes * 60 + seconds
                        logger.info(f"Parsed M:S format: {result}")
                        return result

                # Try direct float conversion
                result = int(float(duration_str))
                logger.info(f"Parsed as float: {result}")
                return result

        except Exception as e:
            logger.error(f"Could not convert duration {duration} to seconds: {e}")
            logger.error(f"Duration type: {type(duration)}")
            logger.error(f"Duration dir: {dir(duration)}")
            return None

    def _calculate_pace(self, distance, moving_time) -> float:
        """Calculate pace in min/km"""
        if not distance or not moving_time:
            return None

        distance_km = float(distance) / 1000

        # Safely get moving time in seconds
        moving_time_seconds = self._safe_duration_to_seconds(moving_time)
        if not moving_time_seconds:
            return None

        time_minutes = moving_time_seconds / 60

        return time_minutes / distance_km if distance_km > 0 else None
    
    def _map_workout_type(self, workout_type: int) -> str:
        """Map Strava workout type to string"""
        workout_types = {
            0: 'default',
            1: 'race',
            2: 'long_run',
            3: 'workout',
            10: 'interval',
            11: 'tempo',
            12: 'recovery'
        }
        return workout_types.get(workout_type, 'unknown')
    
    def get_athlete_info(self, access_token: str) -> Dict:
        """
        Get athlete profile information from Strava
        """
        client = Client(access_token=access_token)
        
        try:
            athlete = client.get_athlete()
            
            return {
                'strava_id': str(athlete.id),
                'first_name': athlete.firstname,
                'last_name': athlete.lastname,
                'email': getattr(athlete, 'email', None),
                'city': athlete.city,
                'state': athlete.state,
                'country': athlete.country,
                'sex': athlete.sex,
                'weight': float(athlete.weight) if athlete.weight else None,
                'ftp': float(athlete.ftp) if athlete.ftp else None,
                'max_hr': int(athlete.max_heartrate) if athlete.max_heartrate else None,
                'profile_photo': athlete.profile_medium,
                'premium': athlete.premium,
                'created_at': athlete.created_at,
                'updated_at': athlete.updated_at
            }
            
        except Exception as e:
            logger.error(f"Error getting athlete info: {str(e)}")
            raise
