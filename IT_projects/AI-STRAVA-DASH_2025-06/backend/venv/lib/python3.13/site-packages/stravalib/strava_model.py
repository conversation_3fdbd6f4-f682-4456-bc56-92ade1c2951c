# generated by datamodel-codegen:
#   filename:  <stdin>

from __future__ import annotations

from collections.abc import Mapping, Sequence
from datetime import datetime
from typing import Literal

from pydantic import BaseModel, Field, RootModel


class ActivityTotal(BaseModel):
    """
    A roll-up of metrics pertaining to a set of activities. Values are in seconds and meters.
    """

    achievement_count: int | None = None
    """
    The total number of achievements of the considered activities.
    """
    count: int | None = None
    """
    The number of activities considered in this total.
    """
    distance: float | None = None
    """
    The total distance covered by the considered activities.
    """
    elapsed_time: int | None = None
    """
    The total elapsed time of the considered activities.
    """
    elevation_gain: float | None = None
    """
    The total elevation gain of the considered activities.
    """
    moving_time: int | None = None
    """
    The total moving time of the considered activities.
    """


class ActivityType(
    RootModel[
        Literal[
            "AlpineSki",
            "BackcountrySki",
            "Canoeing",
            "Crossfit",
            "EBikeRide",
            "Elliptical",
            "Golf",
            "Handcycle",
            "Hike",
            "IceSkate",
            "InlineSkate",
            "Kayaking",
            "Kitesurf",
            "NordicSki",
            "Ride",
            "RockClimbing",
            "RollerSki",
            "Rowing",
            "Run",
            "Sail",
            "Skateboard",
            "Snowboard",
            "Snowshoe",
            "Soccer",
            "StairStepper",
            "StandUpPaddling",
            "Surfing",
            "Swim",
            "Velomobile",
            "VirtualRide",
            "VirtualRun",
            "Walk",
            "WeightTraining",
            "Wheelchair",
            "Windsurf",
            "Workout",
            "Yoga",
        ]
    ]
):
    root: Literal[
        "AlpineSki",
        "BackcountrySki",
        "Canoeing",
        "Crossfit",
        "EBikeRide",
        "Elliptical",
        "Golf",
        "Handcycle",
        "Hike",
        "IceSkate",
        "InlineSkate",
        "Kayaking",
        "Kitesurf",
        "NordicSki",
        "Ride",
        "RockClimbing",
        "RollerSki",
        "Rowing",
        "Run",
        "Sail",
        "Skateboard",
        "Snowboard",
        "Snowshoe",
        "Soccer",
        "StairStepper",
        "StandUpPaddling",
        "Surfing",
        "Swim",
        "Velomobile",
        "VirtualRide",
        "VirtualRun",
        "Walk",
        "WeightTraining",
        "Wheelchair",
        "Windsurf",
        "Workout",
        "Yoga",
    ]
    """
    An enumeration of the types an activity may have. Note that this enumeration does not include new sport types (e.g. MountainBikeRide, EMountainBikeRide), activities with these sport types will have the corresponding activity type (e.g. Ride for MountainBikeRide, EBikeRide for EMountainBikeRide)
    """


class BaseStream(BaseModel):
    original_size: int | None = None
    """
    The number of data points in this stream
    """
    resolution: Literal["low", "medium", "high"] | None = None
    """
    The level of detail (sampling) in which this stream was returned
    """
    series_type: Literal["distance", "time"] | None = None
    """
    The base series used in the case the stream was downsampled
    """


class CadenceStream(BaseStream):
    data: Sequence[int] | None = None
    """
    The sequence of cadence values for this stream, in rotations per minute
    """


class ClubAthlete(BaseModel):
    admin: bool | None = None
    """
    Whether the athlete is a club admin.
    """
    firstname: str | None = None
    """
    The athlete's first name.
    """
    lastname: str | None = None
    """
    The athlete's last initial.
    """
    member: str | None = None
    """
    The athlete's member status.
    """
    owner: bool | None = None
    """
    Whether the athlete is club owner.
    """
    resource_state: int | None = None
    """
    Resource state, indicates level of detail. Possible values: 1 -> "meta", 2 -> "summary", 3 -> "detail"
    """


class DistanceStream(BaseStream):
    data: Sequence[float] | None = None
    """
    The sequence of distance values for this stream, in meters
    """


class Error(BaseModel):
    code: str | None = None
    """
    The code associated with this error.
    """
    field: str | None = None
    """
    The specific field or aspect of the resource associated with this error.
    """
    resource: str | None = None
    """
    The type of resource associated with this error.
    """


class Fault(BaseModel):
    """
    Encapsulates the errors that may be returned from the API.
    """

    errors: Sequence[Error] | None = None
    """
    The set of specific errors associated with this fault, if any.
    """
    message: str | None = None
    """
    The message of the fault.
    """


class HeartrateStream(BaseStream):
    data: Sequence[int] | None = None
    """
    The sequence of heart rate values for this stream, in beats per minute
    """


class LatLng(RootModel[Sequence[float]]):
    """
    A pair of latitude/longitude coordinates, represented as an array of 2 floating point numbers.
    """

    root: Sequence[float] = Field(..., max_length=2, min_length=2)
    """
    A pair of latitude/longitude coordinates, represented as an array of 2 floating point numbers.
    """


class LatLngStream(BaseStream):
    data: Sequence[LatLng] | None = None
    """
    The sequence of lat/long values for this stream
    """


class MembershipApplication(BaseModel):
    active: bool | None = None
    """
    Whether the membership is currently active
    """
    membership: Literal["member", "pending"] | None = None
    """
    The membership status of this application
    """
    success: bool | None = None
    """
    Whether the application for membership was successfully submitted
    """


class MetaActivity(BaseModel):
    id: int | None = None
    """
    The unique identifier of the activity
    """


class MetaAthlete(BaseModel):
    id: int | None = None
    """
    The unique identifier of the athlete
    """


class MetaClub(BaseModel):
    id: int | None = None
    """
    The club's unique identifier.
    """
    name: str | None = None
    """
    The club's name.
    """
    resource_state: int | None = None
    """
    Resource state, indicates level of detail. Possible values: 1 -> "meta", 2 -> "summary", 3 -> "detail"
    """


class MovingStream(BaseStream):
    data: Sequence[bool] | None = None
    """
    The sequence of moving values for this stream, as boolean values
    """


class Primary(BaseModel):
    id: int | None = None
    source: int | None = None
    unique_id: str | None = None
    urls: Mapping[str, str] | None = None


class PhotosSummary(BaseModel):
    count: int | None = None
    """
    The number of photos
    """
    primary: Primary | None = None


class PolylineMap(BaseModel):
    id: str | None = None
    """
    The identifier of the map
    """
    polyline: str | None = None
    """
    The polyline of the map, only returned on detailed representation of an object
    """
    summary_polyline: str | None = None
    """
    The summary polyline of the map
    """


class PowerStream(BaseStream):
    data: Sequence[int] | None = None
    """
    The sequence of power values for this stream, in watts
    """


class SmoothGradeStream(BaseStream):
    data: Sequence[float] | None = None
    """
    The sequence of grade values for this stream, as percents of a grade
    """


class SmoothVelocityStream(BaseStream):
    data: Sequence[float] | None = None
    """
    The sequence of velocity values for this stream, in meters per second
    """


class Split(BaseModel):
    average_speed: float | None = None
    """
    The average speed of this split, in meters per second
    """
    distance: float | None = None
    """
    The distance of this split, in meters
    """
    elapsed_time: int | None = None
    """
    The elapsed time of this split, in seconds
    """
    elevation_difference: float | None = None
    """
    The elevation difference of this split, in meters
    """
    moving_time: int | None = None
    """
    The moving time of this split, in seconds
    """
    pace_zone: int | None = None
    """
    The pacing zone of this split
    """
    split: int | None = None
    """
    N/A
    """


class SportType(
    RootModel[
        Literal[
            "AlpineSki",
            "BackcountrySki",
            "Badminton",
            "Canoeing",
            "Crossfit",
            "EBikeRide",
            "Elliptical",
            "EMountainBikeRide",
            "Golf",
            "GravelRide",
            "Handcycle",
            "HighIntensityIntervalTraining",
            "Hike",
            "IceSkate",
            "InlineSkate",
            "Kayaking",
            "Kitesurf",
            "MountainBikeRide",
            "NordicSki",
            "Pickleball",
            "Pilates",
            "Racquetball",
            "Ride",
            "RockClimbing",
            "RollerSki",
            "Rowing",
            "Run",
            "Sail",
            "Skateboard",
            "Snowboard",
            "Snowshoe",
            "Soccer",
            "Squash",
            "StairStepper",
            "StandUpPaddling",
            "Surfing",
            "Swim",
            "TableTennis",
            "Tennis",
            "TrailRun",
            "Velomobile",
            "VirtualRide",
            "VirtualRow",
            "VirtualRun",
            "Walk",
            "WeightTraining",
            "Wheelchair",
            "Windsurf",
            "Workout",
            "Yoga",
        ]
    ]
):
    root: Literal[
        "AlpineSki",
        "BackcountrySki",
        "Badminton",
        "Canoeing",
        "Crossfit",
        "EBikeRide",
        "Elliptical",
        "EMountainBikeRide",
        "Golf",
        "GravelRide",
        "Handcycle",
        "HighIntensityIntervalTraining",
        "Hike",
        "IceSkate",
        "InlineSkate",
        "Kayaking",
        "Kitesurf",
        "MountainBikeRide",
        "NordicSki",
        "Pickleball",
        "Pilates",
        "Racquetball",
        "Ride",
        "RockClimbing",
        "RollerSki",
        "Rowing",
        "Run",
        "Sail",
        "Skateboard",
        "Snowboard",
        "Snowshoe",
        "Soccer",
        "Squash",
        "StairStepper",
        "StandUpPaddling",
        "Surfing",
        "Swim",
        "TableTennis",
        "Tennis",
        "TrailRun",
        "Velomobile",
        "VirtualRide",
        "VirtualRow",
        "VirtualRun",
        "Walk",
        "WeightTraining",
        "Wheelchair",
        "Windsurf",
        "Workout",
        "Yoga",
    ]
    """
    An enumeration of the sport types an activity may have. Distinct from ActivityType in that it has new types (e.g. MountainBikeRide)
    """


class StreamType(
    RootModel[
        Literal[
            "time",
            "distance",
            "latlng",
            "altitude",
            "velocity_smooth",
            "heartrate",
            "cadence",
            "watts",
            "temp",
            "moving",
            "grade_smooth",
        ]
    ]
):
    root: Literal[
        "time",
        "distance",
        "latlng",
        "altitude",
        "velocity_smooth",
        "heartrate",
        "cadence",
        "watts",
        "temp",
        "moving",
        "grade_smooth",
    ]
    """
    An enumeration of the supported types of streams.
    """


class SummaryActivity(MetaActivity):
    achievement_count: int | None = None
    """
    The number of achievements gained during this activity
    """
    athlete: MetaAthlete | None = None
    athlete_count: int | None = Field(None, ge=1)
    """
    The number of athletes for taking part in a group activity
    """
    average_speed: float | None = None
    """
    The activity's average speed, in meters per second
    """
    average_watts: float | None = None
    """
    Average power output in watts during this activity. Rides only
    """
    comment_count: int | None = None
    """
    The number of comments for this activity
    """
    commute: bool | None = None
    """
    Whether this activity is a commute
    """
    device_watts: bool | None = None
    """
    Whether the watts are from a power meter, false if estimated
    """
    distance: float | None = None
    """
    The activity's distance, in meters
    """
    elapsed_time: int | None = None
    """
    The activity's elapsed time, in seconds
    """
    elev_high: float | None = None
    """
    The activity's highest elevation, in meters
    """
    elev_low: float | None = None
    """
    The activity's lowest elevation, in meters
    """
    end_latlng: LatLng | None = None
    external_id: str | None = None
    """
    The identifier provided at upload time
    """
    flagged: bool | None = None
    """
    Whether this activity is flagged
    """
    gear_id: str | None = None
    """
    The id of the gear for the activity
    """
    has_kudoed: bool | None = None
    """
    Whether the logged-in athlete has kudoed this activity
    """
    hide_from_home: bool | None = None
    """
    Whether the activity is muted
    """
    kilojoules: float | None = None
    """
    The total work done in kilojoules during this activity. Rides only
    """
    kudos_count: int | None = None
    """
    The number of kudos given for this activity
    """
    manual: bool | None = None
    """
    Whether this activity was created manually
    """
    map: PolylineMap | None = None
    max_speed: float | None = None
    """
    The activity's max speed, in meters per second
    """
    max_watts: int | None = None
    """
    Rides with power meter data only
    """
    moving_time: int | None = None
    """
    The activity's moving time, in seconds
    """
    name: str | None = None
    """
    The name of the activity
    """
    photo_count: int | None = None
    """
    The number of Instagram photos for this activity
    """
    private: bool | None = None
    """
    Whether this activity is private
    """
    sport_type: SportType | None = None
    start_date: datetime | None = None
    """
    The time at which the activity was started.
    """
    start_date_local: datetime | None = None
    """
    The time at which the activity was started in the local timezone.
    """
    start_latlng: LatLng | None = None
    timezone: str | None = None
    """
    The timezone of the activity
    """
    total_elevation_gain: float | None = None
    """
    The activity's total elevation gain.
    """
    total_photo_count: int | None = None
    """
    The number of Instagram and Strava photos for this activity
    """
    trainer: bool | None = None
    """
    Whether this activity was recorded on a training machine
    """
    type: ActivityType | None = None
    """
    Deprecated. Prefer to use sport_type
    """
    upload_id: int | None = None
    """
    The identifier of the upload that resulted in this activity
    """
    upload_id_str: str | None = None
    """
    The unique identifier of the upload in string format
    """
    weighted_average_watts: int | None = None
    """
    Similar to Normalized Power. Rides with power meter data only
    """
    workout_type: int | None = None
    """
    The activity's workout type
    """


class SummaryAthlete(MetaAthlete):
    city: str | None = None
    """
    The athlete's city.
    """
    country: str | None = None
    """
    The athlete's country.
    """
    created_at: datetime | None = None
    """
    The time at which the athlete was created.
    """
    firstname: str | None = None
    """
    The athlete's first name.
    """
    lastname: str | None = None
    """
    The athlete's last name.
    """
    premium: bool | None = None
    """
    Deprecated.  Use summit field instead. Whether the athlete has any Summit subscription.
    """
    profile: str | None = None
    """
    URL to a 124x124 pixel profile picture.
    """
    profile_medium: str | None = None
    """
    URL to a 62x62 pixel profile picture.
    """
    resource_state: int | None = None
    """
    Resource state, indicates level of detail. Possible values: 1 -> "meta", 2 -> "summary", 3 -> "detail"
    """
    sex: Literal["M", "F"] | None = None
    """
    The athlete's sex.
    """
    state: str | None = None
    """
    The athlete's state or geographical region.
    """
    summit: bool | None = None
    """
    Whether the athlete has any Summit subscription.
    """
    updated_at: datetime | None = None
    """
    The time at which the athlete was last updated.
    """


class SummaryClub(MetaClub):
    activity_types: Sequence[ActivityType] | None = None
    """
    The activity types that count for a club. This takes precedence over sport_type.
    """
    city: str | None = None
    """
    The club's city.
    """
    country: str | None = None
    """
    The club's country.
    """
    cover_photo: str | None = None
    """
    URL to a ~1185x580 pixel cover photo.
    """
    cover_photo_small: str | None = None
    """
    URL to a ~360x176  pixel cover photo.
    """
    featured: bool | None = None
    """
    Whether the club is featured or not.
    """
    member_count: int | None = None
    """
    The club's member count.
    """
    private: bool | None = None
    """
    Whether the club is private.
    """
    profile_medium: str | None = None
    """
    URL to a 60x60 pixel profile picture.
    """
    sport_type: Literal["cycling", "running", "triathlon", "other"] | None = (
        None
    )
    """
    Deprecated. Prefer to use activity_types.
    """
    state: str | None = None
    """
    The club's state or geographical region.
    """
    url: str | None = None
    """
    The club's vanity URL.
    """
    verified: bool | None = None
    """
    Whether the club is verified or not.
    """


class SummaryGear(BaseModel):
    distance: float | None = None
    """
    The distance logged with this gear.
    """
    id: str | None = None
    """
    The gear's unique identifier.
    """
    name: str | None = None
    """
    The gear's name.
    """
    primary: bool | None = None
    """
    Whether this gear's is the owner's default one.
    """
    resource_state: int | None = None
    """
    Resource state, indicates level of detail. Possible values: 2 -> "summary", 3 -> "detail"
    """


class SummaryPRSegmentEffort(BaseModel):
    effort_count: int | None = None
    """
    Number of efforts by the authenticated athlete on this segment.
    """
    pr_activity_id: int | None = None
    """
    The unique identifier of the activity related to the PR effort.
    """
    pr_date: datetime | None = None
    """
    The time at which the PR effort was started.
    """
    pr_elapsed_time: int | None = None
    """
    The elapsed time ot the PR effort.
    """


class SummarySegmentEffort(BaseModel):
    activity_id: int | None = None
    """
    The unique identifier of the activity related to this effort
    """
    distance: float | None = None
    """
    The effort's distance in meters
    """
    elapsed_time: int | None = None
    """
    The effort's elapsed time
    """
    id: int | None = None
    """
    The unique identifier of this effort
    """
    is_kom: bool | None = None
    """
    Whether this effort is the current best on the leaderboard
    """
    start_date: datetime | None = None
    """
    The time at which the effort was started.
    """
    start_date_local: datetime | None = None
    """
    The time at which the effort was started in the local timezone.
    """


class TemperatureStream(BaseStream):
    data: Sequence[int] | None = None
    """
    The sequence of temperature values for this stream, in celsius degrees
    """


class TimeStream(BaseStream):
    data: Sequence[int] | None = None
    """
    The sequence of time values for this stream, in seconds
    """


class UpdatableActivity(BaseModel):
    commute: bool | None = None
    """
    Whether this activity is a commute
    """
    description: str | None = None
    """
    The description of the activity
    """
    gear_id: str | None = None
    """
    Identifier for the gear associated with the activity. ‘none’ clears gear from activity
    """
    hide_from_home: bool | None = None
    """
    Whether this activity is muted
    """
    name: str | None = None
    """
    The name of the activity
    """
    sport_type: SportType | None = None
    trainer: bool | None = None
    """
    Whether this activity was recorded on a training machine
    """
    type: ActivityType | None = None
    """
    Deprecated. Prefer to use sport_type. In a request where both type and sport_type are present, this field will be ignored
    """


class Upload(BaseModel):
    activity_id: int | None = None
    """
    The identifier of the activity this upload resulted into
    """
    error: str | None = None
    """
    The error associated with this upload
    """
    external_id: str | None = None
    """
    The external identifier of the upload
    """
    id: int | None = None
    """
    The unique identifier of the upload
    """
    id_str: str | None = None
    """
    The unique identifier of the upload in string format
    """
    status: str | None = None
    """
    The status of this upload
    """


class Waypoint(BaseModel):
    categories: Sequence[str] | None = Field(None, min_length=0)
    """
    Categories that the waypoint belongs to
    """
    description: str | None = None
    """
    A description of the waypoint (optional)
    """
    distance_into_route: int | None = None
    """
    The number meters along the route that the waypoint is located
    """
    latlng: LatLng | None = None
    """
    The location along the route that the waypoint is closest to
    """
    target_latlng: LatLng | None = None
    """
    A location off of the route that the waypoint is (optional)
    """
    title: str | None = None
    """
    A title for the waypoint
    """


class ZoneRange(BaseModel):
    max: int | None = None
    """
    The maximum value in the range.
    """
    min: int | None = None
    """
    The minimum value in the range.
    """


class ZoneRanges(RootModel[Sequence[ZoneRange]]):
    root: Sequence[ZoneRange]


class ActivityStats(BaseModel):
    """
    A set of rolled-up statistics and totals for an athlete
    """

    all_ride_totals: ActivityTotal | None = None
    """
    The all time ride stats for the athlete.
    """
    all_run_totals: ActivityTotal | None = None
    """
    The all time run stats for the athlete.
    """
    all_swim_totals: ActivityTotal | None = None
    """
    The all time swim stats for the athlete.
    """
    biggest_climb_elevation_gain: float | None = None
    """
    The highest climb ridden by the athlete.
    """
    biggest_ride_distance: float | None = None
    """
    The longest distance ridden by the athlete.
    """
    recent_ride_totals: ActivityTotal | None = None
    """
    The recent (last 4 weeks) ride stats for the athlete.
    """
    recent_run_totals: ActivityTotal | None = None
    """
    The recent (last 4 weeks) run stats for the athlete.
    """
    recent_swim_totals: ActivityTotal | None = None
    """
    The recent (last 4 weeks) swim stats for the athlete.
    """
    ytd_ride_totals: ActivityTotal | None = None
    """
    The year to date ride stats for the athlete.
    """
    ytd_run_totals: ActivityTotal | None = None
    """
    The year to date run stats for the athlete.
    """
    ytd_swim_totals: ActivityTotal | None = None
    """
    The year to date swim stats for the athlete.
    """


class AltitudeStream(BaseStream):
    data: Sequence[float] | None = None
    """
    The sequence of altitude values for this stream, in meters
    """


class ClubActivity(BaseModel):
    athlete: MetaAthlete | None = None
    distance: float | None = None
    """
    The activity's distance, in meters
    """
    elapsed_time: int | None = None
    """
    The activity's elapsed time, in seconds
    """
    moving_time: int | None = None
    """
    The activity's moving time, in seconds
    """
    name: str | None = None
    """
    The name of the activity
    """
    sport_type: SportType | None = None
    total_elevation_gain: float | None = None
    """
    The activity's total elevation gain.
    """
    type: ActivityType | None = None
    """
    Deprecated. Prefer to use sport_type
    """
    workout_type: int | None = None
    """
    The activity's workout type
    """


class ClubAnnouncement(BaseModel):
    athlete: SummaryAthlete | None = None
    club_id: int | None = None
    """
    The unique identifier of the club this announcements was made in.
    """
    created_at: datetime | None = None
    """
    The time at which this announcement was created.
    """
    id: int | None = None
    """
    The unique identifier of this announcement.
    """
    message: str | None = None
    """
    The content of this announcement
    """


class Comment(BaseModel):
    activity_id: int | None = None
    """
    The identifier of the activity this comment is related to
    """
    athlete: SummaryAthlete | None = None
    created_at: datetime | None = None
    """
    The time at which this comment was created.
    """
    id: int | None = None
    """
    The unique identifier of this comment
    """
    text: str | None = None
    """
    The content of the comment
    """


class DetailedAthlete(SummaryAthlete):
    bikes: Sequence[SummaryGear] | None = None
    """
    The athlete's bikes.
    """
    clubs: Sequence[SummaryClub] | None = None
    """
    The athlete's clubs.
    """
    follower_count: int | None = None
    """
    The athlete's follower count.
    """
    friend_count: int | None = None
    """
    The athlete's friend count.
    """
    ftp: int | None = None
    """
    The athlete's FTP (Functional Threshold Power).
    """
    measurement_preference: Literal["feet", "meters"] | None = None
    """
    The athlete's preferred unit system.
    """
    shoes: Sequence[SummaryGear] | None = None
    """
    The athlete's shoes.
    """
    weight: float | None = None
    """
    The athlete's weight.
    """


class DetailedClub(SummaryClub):
    admin: bool | None = None
    """
    Whether the currently logged-in athlete is an administrator of this club.
    """
    following_count: int | None = None
    """
    The number of athletes in the club that the logged-in athlete follows.
    """
    membership: Literal["member", "pending"] | None = None
    """
    The membership status of the logged-in athlete.
    """
    owner: bool | None = None
    """
    Whether the currently logged-in athlete is the owner of this club.
    """


class DetailedGear(SummaryGear):
    brand_name: str | None = None
    """
    The gear's brand name.
    """
    description: str | None = None
    """
    The gear's description.
    """
    frame_type: int | None = None
    """
    The gear's frame type (bike only).
    """
    model_name: str | None = None
    """
    The gear's model name.
    """


class ExplorerSegment(BaseModel):
    avg_grade: float | None = None
    """
    The segment's average grade, in percents
    """
    climb_category: int | None = Field(None, ge=0, le=5)
    """
    The category of the climb [0, 5]. Higher is harder ie. 5 is Hors catégorie, 0 is uncategorized in climb_category. If climb_category = 5, climb_category_desc = HC. If climb_category = 2, climb_category_desc = 3.
    """
    climb_category_desc: Literal["NC", "4", "3", "2", "1", "HC"] | None = None
    """
    The description for the category of the climb
    """
    distance: float | None = None
    """
    The segment's distance, in meters
    """
    elev_difference: float | None = None
    """
    The segments's evelation difference, in meters
    """
    end_latlng: LatLng | None = None
    id: int | None = None
    """
    The unique identifier of this segment
    """
    name: str | None = None
    """
    The name of this segment
    """
    points: str | None = None
    """
    The polyline of the segment
    """
    start_latlng: LatLng | None = None


class HeartRateZoneRanges(BaseModel):
    custom_zones: bool | None = None
    """
    Whether the athlete has set their own custom heart rate zones
    """
    zones: ZoneRanges | None = None


class Lap(BaseModel):
    activity: MetaActivity | None = None
    athlete: MetaAthlete | None = None
    average_cadence: float | None = None
    """
    The lap's average cadence
    """
    average_speed: float | None = None
    """
    The lap's average speed
    """
    distance: float | None = None
    """
    The lap's distance, in meters
    """
    elapsed_time: int | None = None
    """
    The lap's elapsed time, in seconds
    """
    end_index: int | None = None
    """
    The end index of this effort in its activity's stream
    """
    id: int | None = None
    """
    The unique identifier of this lap
    """
    lap_index: int | None = None
    """
    The index of this lap in the activity it belongs to
    """
    max_speed: float | None = None
    """
    The maximum speed of this lat, in meters per second
    """
    moving_time: int | None = None
    """
    The lap's moving time, in seconds
    """
    name: str | None = None
    """
    The name of the lap
    """
    pace_zone: int | None = None
    """
    The athlete's pace zone during this lap
    """
    split: int | None = None
    start_date: datetime | None = None
    """
    The time at which the lap was started.
    """
    start_date_local: datetime | None = None
    """
    The time at which the lap was started in the local timezone.
    """
    start_index: int | None = None
    """
    The start index of this effort in its activity's stream
    """
    total_elevation_gain: float | None = None
    """
    The elevation gain of this lap, in meters
    """


class PowerZoneRanges(BaseModel):
    zones: ZoneRanges | None = None


class StreamSet(BaseModel):
    altitude: AltitudeStream | None = None
    cadence: CadenceStream | None = None
    distance: DistanceStream | None = None
    grade_smooth: SmoothGradeStream | None = None
    heartrate: HeartrateStream | None = None
    latlng: LatLngStream | None = None
    moving: MovingStream | None = None
    temp: TemperatureStream | None = None
    time: TimeStream | None = None
    velocity_smooth: SmoothVelocityStream | None = None
    watts: PowerStream | None = None


class SummarySegment(BaseModel):
    activity_type: Literal["Ride", "Run"] | None = None
    athlete_pr_effort: SummaryPRSegmentEffort | None = None
    athlete_segment_stats: SummarySegmentEffort | None = None
    average_grade: float | None = None
    """
    The segment's average grade, in percents
    """
    city: str | None = None
    """
    The segments's city.
    """
    climb_category: int | None = None
    """
    The category of the climb [0, 5]. Higher is harder ie. 5 is Hors catégorie, 0 is uncategorized in climb_category.
    """
    country: str | None = None
    """
    The segment's country.
    """
    distance: float | None = None
    """
    The segment's distance, in meters
    """
    elevation_high: float | None = None
    """
    The segments's highest elevation, in meters
    """
    elevation_low: float | None = None
    """
    The segments's lowest elevation, in meters
    """
    end_latlng: LatLng | None = None
    id: int | None = None
    """
    The unique identifier of this segment
    """
    maximum_grade: float | None = None
    """
    The segments's maximum grade, in percents
    """
    name: str | None = None
    """
    The name of this segment
    """
    private: bool | None = None
    """
    Whether this segment is private.
    """
    start_latlng: LatLng | None = None
    state: str | None = None
    """
    The segments's state or geographical region.
    """


class TimedZoneRange(ZoneRange):
    """
    A union type representing the time spent in a given zone.
    """

    time: int | None = None
    """
    The number of seconds spent in this zone
    """


class Zones(BaseModel):
    heart_rate: HeartRateZoneRanges | None = None
    power: PowerZoneRanges | None = None


class DetailedSegment(SummarySegment):
    athlete_count: int | None = None
    """
    The number of unique athletes who have an effort for this segment
    """
    created_at: datetime | None = None
    """
    The time at which the segment was created.
    """
    effort_count: int | None = None
    """
    The total number of efforts for this segment
    """
    hazardous: bool | None = None
    """
    Whether this segment is considered hazardous
    """
    map: PolylineMap | None = None
    star_count: int | None = None
    """
    The number of stars for this segment
    """
    total_elevation_gain: float | None = None
    """
    The segment's total elevation gain.
    """
    updated_at: datetime | None = None
    """
    The time at which the segment was last updated.
    """


class DetailedSegmentEffort(SummarySegmentEffort):
    activity: MetaActivity | None = None
    athlete: MetaAthlete | None = None
    average_cadence: float | None = None
    """
    The effort's average cadence
    """
    average_heartrate: float | None = None
    """
    The heart heart rate of the athlete during this effort
    """
    average_watts: float | None = None
    """
    The average wattage of this effort
    """
    device_watts: bool | None = None
    """
    For riding efforts, whether the wattage was reported by a dedicated recording device
    """
    end_index: int | None = None
    """
    The end index of this effort in its activity's stream
    """
    hidden: bool | None = None
    """
    Whether this effort should be hidden when viewed within an activity
    """
    kom_rank: int | None = Field(None, ge=1, le=10)
    """
    The rank of the effort on the global leaderboard if it belongs in the top 10 at the time of upload
    """
    max_heartrate: float | None = None
    """
    The maximum heart rate of the athlete during this effort
    """
    moving_time: int | None = None
    """
    The effort's moving time
    """
    name: str | None = None
    """
    The name of the segment on which this effort was performed
    """
    pr_rank: int | None = Field(None, ge=1, le=3)
    """
    The rank of the effort on the athlete's leaderboard if it belongs in the top 3 at the time of upload
    """
    segment: SummarySegment | None = None
    start_index: int | None = None
    """
    The start index of this effort in its activity's stream
    """


class ExplorerResponse(BaseModel):
    segments: Sequence[ExplorerSegment] | None = None
    """
    The set of segments matching an explorer request
    """


class Route(BaseModel):
    athlete: SummaryAthlete | None = None
    created_at: datetime | None = None
    """
    The time at which the route was created
    """
    description: str | None = None
    """
    The description of the route
    """
    distance: float | None = None
    """
    The route's distance, in meters
    """
    elevation_gain: float | None = None
    """
    The route's elevation gain.
    """
    estimated_moving_time: int | None = None
    """
    Estimated time in seconds for the authenticated athlete to complete route
    """
    id: int | None = None
    """
    The unique identifier of this route
    """
    id_str: str | None = None
    """
    The unique identifier of the route in string format
    """
    map: PolylineMap | None = None
    name: str | None = None
    """
    The name of this route
    """
    private: bool | None = None
    """
    Whether this route is private
    """
    segments: Sequence[SummarySegment] | None = None
    """
    The segments traversed by this route
    """
    starred: bool | None = None
    """
    Whether this route is starred by the logged-in athlete
    """
    sub_type: int | None = None
    """
    This route's sub-type (1 for road, 2 for mountain bike, 3 for cross, 4 for trail, 5 for mixed)
    """
    timestamp: int | None = None
    """
    An epoch timestamp of when the route was created
    """
    type: int | None = None
    """
    This route's type (1 for ride, 2 for runs)
    """
    updated_at: datetime | None = None
    """
    The time at which the route was last updated
    """
    waypoints: Sequence[Waypoint] | None = Field(None, min_length=0)
    """
    The custom waypoints along this route
    """


class TimedZoneDistribution(RootModel[Sequence[TimedZoneRange]]):
    """
    Stores the exclusive ranges representing zones and the time spent in each.
    """

    root: Sequence[TimedZoneRange]
    """
    Stores the exclusive ranges representing zones and the time spent in each.
    """


class ActivityZone(BaseModel):
    custom_zones: bool | None = None
    distribution_buckets: TimedZoneDistribution | None = None
    max: int | None = None
    points: int | None = None
    score: int | None = None
    sensor_based: bool | None = None
    type: Literal["heartrate", "power"] | None = None


class DetailedActivity(SummaryActivity):
    best_efforts: Sequence[DetailedSegmentEffort] | None = None
    calories: float | None = None
    """
    The number of kilocalories consumed during this activity
    """
    description: str | None = None
    """
    The description of the activity
    """
    device_name: str | None = None
    """
    The name of the device used to record the activity
    """
    embed_token: str | None = None
    """
    The token used to embed a Strava activity
    """
    gear: SummaryGear | None = None
    laps: Sequence[Lap] | None = None
    photos: PhotosSummary | None = None
    segment_efforts: Sequence[DetailedSegmentEffort] | None = None
    splits_metric: Sequence[Split] | None = None
    """
    The splits of this activity in metric units (for runs)
    """
    splits_standard: Sequence[Split] | None = None
    """
    The splits of this activity in imperial units (for runs)
    """
