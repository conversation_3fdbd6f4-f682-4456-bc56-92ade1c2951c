{"swagger": "2.0", "info": {"title": "Strava API v3", "description": "The [Swagger Playground](https://developers.strava.com/playground) is the easiest way to familiarize yourself with the Strava API by submitting HTTP requests and observing the responses before you write any client code. It will show what a response will look like with different endpoints depending on the authorization scope you receive from your athletes. To use the Playground, go to https://www.strava.com/settings/api and change your “Authorization Callback Domain” to developers.strava.com. Please note, we only support Swagger 2.0. There is a known issue where you can only select one scope at a time. For more information, please check the section “client code” at https://developers.strava.com/docs.", "version": "3.0.0"}, "host": "www.strava.com", "schemes": ["https"], "basePath": "/api/v3", "produces": ["application/json"], "securityDefinitions": {"strava_oauth": {"type": "oauth2", "flow": "accessCode", "authorizationUrl": "https://www.strava.com/api/v3/oauth/authorize", "tokenUrl": "https://www.strava.com/api/v3/oauth/token", "scopes": {"read": "Read public segments, public routes, public profile data, public posts, public events, club feeds, and leaderboards", "read_all": "Read private routes, private segments, and private events for the user", "profile:read_all": "Read all profile information even if the user has set their profile visibility to Followers or Only You", "profile:write": "Update the user's weight and Functional Threshold Power (FTP), and access to star or unstar segments on their behalf", "activity:read": "Read the user's activity data for activities that are visible to Everyone and Followers, excluding privacy zone data", "activity:read_all": "The same access as activity:read, plus privacy zone data and access to read the user's activities with visibility set to Only You", "activity:write": "Access to create manual activities and uploads, and access to edit any activities that are visible to the app, based on activity read access level"}}}, "security": [{"strava_oauth": ["public"]}], "parameters": {"page": {"name": "page", "in": "query", "description": "Page number. Defaults to 1.", "type": "integer"}, "perPage": {"name": "per_page", "in": "query", "description": "Number of items per page. Defaults to 30.", "type": "integer", "default": 30}}, "paths": {"/athletes/{id}/stats": {"get": {"operationId": "getStats", "summary": "Get Athlete Stats", "description": "Returns the activity stats of an athlete. Only includes data from activities set to Everyone visibilty.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the athlete. Must match the authenticated athlete.", "required": true, "type": "integer", "format": "int64"}], "tags": ["Athletes"], "responses": {"200": {"description": "Activity stats of the athlete.", "schema": {"$ref": "https://developers.strava.com/swagger/activity_stats.json#/ActivityStats"}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/athlete": {"get": {"operationId": "getLoggedInAthlete", "summary": "Get Authenticated Athlete", "description": "Returns the currently authenticated athlete. Tokens with profile:read_all scope will receive a detailed athlete representation; all others will receive a summary representation.", "tags": ["Athletes"], "responses": {"200": {"description": "Profile information for the authenticated athlete.", "schema": {"$ref": "https://developers.strava.com/swagger/athlete.json#/DetailedAthlete"}, "examples": {"application/json": {"id": 1234567890987654321, "username": "marianne_t", "resource_state": 3, "firstname": "<PERSON>", "lastname": "<PERSON><PERSON><PERSON>", "city": "San Francisco", "state": "CA", "country": "US", "sex": "F", "premium": true, "created_at": "2017-11-14T02:30:05Z", "updated_at": "2018-02-06T19:32:20Z", "badge_type_id": 4, "profile_medium": "https://xxxxxx.cloudfront.net/pictures/athletes/123456789/123456789/2/medium.jpg", "profile": "https://xxxxx.cloudfront.net/pictures/athletes/123456789/123456789/2/large.jpg", "friend": null, "follower": null, "follower_count": 5, "friend_count": 5, "mutual_friend_count": 0, "athlete_type": 1, "date_preference": "%m/%d/%Y", "measurement_preference": "feet", "clubs": [], "ftp": null, "weight": 0, "bikes": [{"id": "b12345678987655", "primary": true, "name": "EMC", "resource_state": 2, "distance": 0}], "shoes": [{"id": "g12345678987655", "primary": true, "name": "adidas", "resource_state": 2, "distance": 4904}]}}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}, "put": {"operationId": "updateLoggedInAthlete", "summary": "Update Athlete", "description": "Update the currently authenticated athlete. Requires profile:write scope.", "consumes": ["multipart/form-data"], "tags": ["Athletes"], "parameters": [{"name": "weight", "in": "path", "description": "The weight of the athlete in kilograms.", "required": true, "type": "number", "format": "float"}], "responses": {"200": {"description": "Profile information for the authenticated athlete.", "schema": {"$ref": "https://developers.strava.com/swagger/athlete.json#/DetailedAthlete"}, "examples": {"application/json": {"id": 12345678987655098765444, "username": "marianne_v", "resource_state": 3, "firstname": "<PERSON>", "lastname": "V.", "city": "San Francisco", "state": "CA", "country": "US", "sex": "F", "premium": true, "created_at": "2017-11-14T02:30:05Z", "updated_at": "2018-02-06T19:32:20Z", "badge_type_id": 4, "profile_medium": "https://xxxxxx.cloudfront.net/pictures/athletes/1234567898765509876/1234567898765509876/2/medium.jpg", "profile": "https://xxxxx.cloudfront.net/pictures/athletes/1234567898765509876/1234567898765509876/2/large.jpg", "friend": null, "follower": null, "follower_count": 5, "friend_count": 5, "mutual_friend_count": 0, "athlete_type": 1, "date_preference": "%m/%d/%Y", "measurement_preference": "feet", "clubs": [], "ftp": null, "weight": 0, "bikes": [{"id": "b1234567898765509876", "primary": true, "name": "EMC", "resource_state": 2, "distance": 0}], "shoes": [{"id": "g1234567898765509876", "primary": true, "name": "adidas", "resource_state": 2, "distance": 4904}]}}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/athlete/zones": {"get": {"operationId": "getLoggedInAthleteZones", "summary": "Get Zones", "description": "Returns the the authenticated athlete's heart rate and power zones. Requires profile:read_all.", "tags": ["Athletes"], "responses": {"200": {"description": "Heart rate and power zones.", "schema": {"$ref": "https://developers.strava.com/swagger/zones.json#/Zones"}, "examples": {"application/json": [{"distribution_buckets": [{"max": 0, "min": 0, "time": 1498}, {"max": 50, "min": 0, "time": 62}, {"max": 100, "min": 50, "time": 169}, {"max": 150, "min": 100, "time": 536}, {"max": 200, "min": 150, "time": 672}, {"max": 250, "min": 200, "time": 821}, {"max": 300, "min": 250, "time": 529}, {"max": 350, "min": 300, "time": 251}, {"max": 400, "min": 350, "time": 80}, {"max": 450, "min": 400, "time": 81}, {"max": -1, "min": 450, "time": 343}], "type": "power", "resource_state": 3, "sensor_based": true}]}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/segments/{id}": {"get": {"operationId": "getSegmentById", "summary": "Get Segment", "description": "Returns the specified segment. read_all scope required in order to retrieve athlete-specific segment information, or to retrieve private segments.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the segment.", "required": true, "type": "integer", "format": "int64"}], "tags": ["Segments"], "responses": {"200": {"description": "Representation of a segment.", "schema": {"$ref": "https://developers.strava.com/swagger/segment.json#/DetailedSegment"}, "examples": {"application/json": {"id": 229781, "resource_state": 3, "name": "Hawk Hill", "activity_type": "Ride", "distance": 2684.82, "average_grade": 5.7, "maximum_grade": 14.2, "elevation_high": 245.3, "elevation_low": 92.4, "start_latlng": [37.8331119, -122.4834356], "end_latlng": [37.8280722, -122.4981393], "climb_category": 1, "city": "San Francisco", "state": "CA", "country": "United States", "private": false, "hazardous": false, "starred": false, "created_at": "2009-09-21T20:29:41Z", "updated_at": "2018-02-15T09:04:18Z", "total_elevation_gain": 155.733, "map": {"id": "s229781", "polyline": "}g|eFnpqjVl@En@Md@HbAd@d@^h@Xx@VbARjBDh@OPQf@w@d@k@XKXDFPH\\EbGT`AV`@v@|@NTNb@?XOb@cAxAWLuE@eAFMBoAv@eBt@q@b@}@tAeAt@i@dAC`AFZj@dB?~@[h@MbAVn@b@b@\\d@Eh@Qb@_@d@eB|@c@h@WfBK|AMpA?VF\\\\t@f@t@h@j@|@b@hCb@b@XTd@Bl@GtA?jAL`ALp@Tr@RXd@Rx@Pn@^Zh@Tx@Zf@`@FTCzDy@f@Yx@m@n@Op@VJr@", "resource_state": 3}, "effort_count": 309974, "athlete_count": 30623, "star_count": 2428, "athlete_segment_stats": {"pr_elapsed_time": 553, "pr_date": "1993-04-03", "effort_count": 2}}}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/segments/starred": {"get": {"operationId": "getLoggedInAthleteStarredSegments", "summary": "List Starred Segments", "description": "List of the authenticated athlete's starred segments. Private segments are filtered out unless requested by a token with read_all scope.", "parameters": [{"$ref": "#/parameters/page"}, {"$ref": "#/parameters/perPage"}], "tags": ["Segments"], "responses": {"200": {"description": "List of the authenticated athlete's starred segments.", "schema": {"type": "array", "items": {"$ref": "https://developers.strava.com/swagger/segment.json#/SummarySegment"}}, "examples": {"application/json": {"id": 229781, "resource_state": 3, "name": "Hawk Hill", "activity_type": "Ride", "distance": 2684.82, "average_grade": 5.7, "maximum_grade": 14.2, "elevation_high": 245.3, "elevation_low": 92.4, "start_latlng": [37.8331119, -122.4834356], "end_latlng": [37.8280722, -122.4981393], "climb_category": 1, "city": "San Francisco", "state": "CA", "country": "United States", "private": false, "hazardous": false, "starred": false, "created_at": "2009-09-21T20:29:41Z", "updated_at": "2018-02-15T09:04:18Z", "total_elevation_gain": 155.733, "map": {"id": "s229781", "polyline": "}g|eFnpqjVl@En@Md@HbAd@d@^h@Xx@VbARjBDh@OPQf@w@d@k@XKXDFPH\\EbGT`AV`@v@|@NTNb@?XOb@cAxAWLuE@eAFMBoAv@eBt@q@b@}@tAeAt@i@dAC`AFZj@dB?~@[h@MbAVn@b@b@\\d@Eh@Qb@_@d@eB|@c@h@WfBK|AMpA?VF\\\\t@f@t@h@j@|@b@hCb@b@XTd@Bl@GtA?jAL`ALp@Tr@RXd@Rx@Pn@^Zh@Tx@Zf@`@FTCzDy@f@Yx@m@n@Op@VJr@", "resource_state": 3}, "effort_count": 309974, "athlete_count": 30623, "star_count": 2428, "athlete_segment_stats": {"pr_elapsed_time": 553, "pr_date": "1993-04-03", "effort_count": 2}}}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/segments/{id}/starred": {"put": {"operationId": "starSegment", "summary": "Star Segment", "description": "Stars/Unstars the given segment for the authenticated athlete. Requires profile:write scope.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the segment to star.", "required": true, "type": "integer", "format": "int64"}, {"name": "starred", "in": "formData", "description": "If true, star the segment; if false, unstar the segment.", "type": "boolean", "required": true, "default": false}], "tags": ["Segments"], "responses": {"200": {"description": "Representation of a segment.", "schema": {"$ref": "https://developers.strava.com/swagger/segment.json#/DetailedSegment"}, "examples": {"application/json": {"id": 229781, "resource_state": 3, "name": "Hawk Hill", "activity_type": "Ride", "distance": 2684.82, "average_grade": 5.7, "maximum_grade": 14.2, "elevation_high": 245.3, "elevation_low": 92.4, "start_latlng": [37.8331119, -122.4834356], "end_latlng": [37.8280722, -122.4981393], "climb_category": 1, "city": "San Francisco", "state": "CA", "country": "United States", "private": false, "hazardous": false, "starred": false, "created_at": "2009-09-21T20:29:41Z", "updated_at": "2018-02-15T09:04:18Z", "total_elevation_gain": 155.733, "map": {"id": "s229781", "polyline": "}g|eFnpqjVl@En@Md@HbAd@d@^h@Xx@VbARjBDh@OPQf@w@d@k@XKXDFPH\\EbGT`AV`@v@|@NTNb@?XOb@cAxAWLuE@eAFMBoAv@eBt@q@b@}@tAeAt@i@dAC`AFZj@dB?~@[h@MbAVn@b@b@\\d@Eh@Qb@_@d@eB|@c@h@WfBK|AMpA?VF\\\\t@f@t@h@j@|@b@hCb@b@XTd@Bl@GtA?jAL`ALp@Tr@RXd@Rx@Pn@^Zh@Tx@Zf@`@FTCzDy@f@Yx@m@n@Op@VJr@", "resource_state": 3}, "effort_count": 309974, "athlete_count": 30623, "star_count": 2428, "athlete_segment_stats": {"pr_elapsed_time": 553, "pr_date": "1993-04-03", "effort_count": 2}}}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/segment_efforts": {"get": {"operationId": "getEffortsBySegmentId", "summary": "List Segment Efforts", "description": "Returns a set of the authenticated athlete's segment efforts for a given segment.  Requires subscription.", "parameters": [{"name": "segment_id", "in": "query", "description": "The identifier of the segment.", "required": true, "type": "integer"}, {"name": "start_date_local", "in": "query", "description": "ISO 8601 formatted date time.", "type": "string", "format": "date-time"}, {"name": "end_date_local", "in": "query", "description": "ISO 8601 formatted date time.", "type": "string", "format": "date-time"}, {"$ref": "#/parameters/perPage", "in": "query"}], "tags": ["SegmentEfforts"], "responses": {"200": {"description": "List of segment efforts.", "schema": {"type": "array", "items": {"$ref": "https://developers.strava.com/swagger/segment_effort.json#/DetailedSegmentEffort"}}, "examples": {"application/json": [{"id": 123456789, "resource_state": 2, "name": "Alpe <PERSON>", "activity": {"id": 1234567890, "resource_state": 1}, "athlete": {"id": 123445678689, "resource_state": 1}, "elapsed_time": 1657, "moving_time": 1642, "start_date": "2007-09-15T08:15:29Z", "start_date_local": "2007-09-15T09:15:29Z", "distance": 6148.92, "start_index": 1102, "end_index": 1366, "device_watts": false, "average_watts": 220.2, "segment": {"id": 788127, "resource_state": 2, "name": "Alpe <PERSON>", "activity_type": "Ride", "distance": 6297.46, "average_grade": 4.8, "maximum_grade": 16.3, "elevation_high": 416, "elevation_low": 104.6, "start_latlng": [52.98501000581467, -3.1869720001197366], "end_latlng": [53.02204074375785, -3.2039630001245736], "climb_category": 2, "city": "Le Bourg D'Oisans", "state": "RA", "country": "France", "private": false, "hazardous": false, "starred": false}, "kom_rank": null, "pr_rank": null, "achievements": []}]}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/segments/explore": {"get": {"operationId": "exploreSegments", "summary": "Explore segments", "description": "Returns the top 10 segments matching a specified query.", "parameters": [{"name": "bounds", "in": "query", "description": "The latitude and longitude for two points describing a rectangular boundary for the search: [southwest corner latitutde, southwest corner longitude, northeast corner latitude, northeast corner longitude]", "required": true, "type": "array", "items": {"type": "number", "format": "float"}, "collectionFormat": "csv", "minItems": 4, "maxItems": 4}, {"name": "activity_type", "in": "query", "description": "Desired activity type.", "type": "string", "enum": ["running", "riding"]}, {"name": "min_cat", "in": "query", "description": "The minimum climbing category.", "type": "integer", "minimum": 0, "maximum": 5}, {"name": "max_cat", "in": "query", "description": "The maximum climbing category.", "type": "integer", "minimum": 0, "maximum": 5}], "tags": ["Segments"], "responses": {"200": {"description": "List of matching segments.", "schema": {"$ref": "https://developers.strava.com/swagger/segment.json#/ExplorerResponse"}, "examples": {"application/json": {"segments": [{"id": 229781, "resource_state": 2, "name": "Hawk Hill", "climb_category": 1, "climb_category_desc": "4", "avg_grade": 5.7, "start_latlng": [37.8331119, -122.4834356], "end_latlng": [37.8280722, -122.4981393], "elev_difference": 152.8, "distance": 2684.8, "points": "}g|eFnpqjVl@En@Md@HbAd@d@^h@Xx@VbARjBDh@OPQf@w@d@k@XKXDFPH\\EbGT`AV`@v@|@NTNb@?XOb@cAxAWLuE@eAFMBoAv@eBt@q@b@}@tAeAt@i@dAC`AFZj@dB?~@[h@MbAVn@b@b@\\d@Eh@Qb@_@d@eB|@c@h@WfBK|AMpA?VF\\\\t@f@t@h@j@|@b@hCb@b@XTd@Bl@GtA?jAL`ALp@Tr@RXd@Rx@Pn@^Zh@Tx@Zf@`@FTCzDy@f@Yx@m@n@Op@VJr@", "starred": false}]}}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/segment_efforts/{id}": {"get": {"operationId": "getSegmentEffortById", "summary": "Get Segment Effort", "description": "Returns a segment effort from an activity that is owned by the authenticated athlete. Requires subscription.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the segment effort.", "required": true, "type": "integer", "format": "int64"}], "tags": ["SegmentEfforts"], "responses": {"200": {"description": "Representation of a segment effort.", "schema": {"$ref": "https://developers.strava.com/swagger/segment_effort.json#/DetailedSegmentEffort"}, "examples": {"application/json": {"id": 1234556789, "resource_state": 3, "name": "Alpe <PERSON>", "activity": {"id": 3454504, "resource_state": 1}, "athlete": {"id": 54321, "resource_state": 1}, "elapsed_time": 381, "moving_time": 340, "start_date": "2018-02-12T16:12:41Z", "start_date_local": "2018-02-12T08:12:41Z", "distance": 83, "start_index": 65, "end_index": 83, "segment": {"id": 63450, "resource_state": 2, "name": "Alpe <PERSON>", "activity_type": "Run", "distance": 780.35, "average_grade": -0.5, "maximum_grade": 0, "elevation_high": 21, "elevation_low": 17.2, "start_latlng": [37.808407654682, -122.426682919323], "end_latlng": [37.808297909724, -122.421324329674], "climb_category": 0, "city": "San Francisco", "state": "CA", "country": "United States", "private": false, "hazardous": false, "starred": false}, "kom_rank": null, "pr_rank": null, "achievements": [], "athlete_segment_stats": {"pr_elapsed_time": 212, "pr_date": "2015-02-12", "effort_count": 149}}}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/activities": {"post": {"operationId": "createActivity", "summary": "Create an Activity", "description": "Creates a manual activity for an athlete, requires activity:write scope.", "parameters": [{"name": "name", "in": "formData", "description": "The name of the activity.", "required": true, "type": "string"}, {"name": "type", "in": "formData", "description": "Type of activity. For example - Run, Ride etc.", "type": "string", "required": false}, {"name": "sport_type", "in": "formData", "description": "Sport type of activity. For example - Run, MountainBikeRide, Ride, etc.", "type": "string", "required": true}, {"name": "start_date_local", "in": "formData", "description": "ISO 8601 formatted date time.", "type": "string", "format": "date-time", "required": true}, {"name": "elapsed_time", "in": "formData", "description": "In seconds.", "type": "integer", "required": true}, {"name": "description", "in": "formData", "description": "Description of the activity.", "type": "string", "required": false}, {"name": "distance", "in": "formData", "description": "In meters.", "type": "number", "format": "float", "required": false}, {"name": "trainer", "in": "formData", "description": "Set to 1 to mark as a trainer activity.", "type": "integer", "required": false}, {"name": "commute", "in": "formData", "description": "Set to 1 to mark as commute.", "type": "integer", "required": false}], "tags": ["Activities"], "responses": {"201": {"description": "The activity's detailed representation.", "schema": {"$ref": "https://developers.strava.com/swagger/activity.json#/DetailedActivity"}, "examples": {"application/json": {"id": 123456778928065, "resource_state": 3, "external_id": null, "upload_id": null, "athlete": {"id": 12343545645788, "resource_state": 1}, "name": "Chill Day", "distance": 0, "moving_time": 18373, "elapsed_time": 18373, "total_elevation_gain": 0, "type": "Ride", "sport_type": "MountainBikeRide", "start_date": "2018-02-20T18:02:13Z", "start_date_local": "2018-02-20T10:02:13Z", "timezone": "(GMT-08:00) America/Los_Angeles", "utc_offset": -28800, "achievement_count": 0, "kudos_count": 0, "comment_count": 0, "athlete_count": 1, "photo_count": 0, "map": {"id": "a12345678908766", "polyline": null, "resource_state": 3}, "trainer": false, "commute": false, "manual": true, "private": false, "flagged": false, "gear_id": "b453542543", "from_accepted_tag": null, "average_speed": 0, "max_speed": 0, "device_watts": false, "has_heartrate": false, "pr_count": 0, "total_photo_count": 0, "has_kudoed": false, "workout_type": null, "description": null, "calories": 0, "segment_efforts": []}}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/activities/{id}": {"get": {"operationId": "getActivityById", "summary": "Get Activity", "description": "Returns the given activity that is owned by the authenticated athlete. Requires activity:read for Everyone and Followers activities. Requires activity:read_all for Only Me activities.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the activity.", "required": true, "type": "integer", "format": "int64"}, {"name": "include_all_efforts", "in": "query", "description": "To include all segments efforts.", "type": "boolean"}], "tags": ["Activities"], "responses": {"200": {"description": "The activity's detailed representation.", "schema": {"$ref": "https://developers.strava.com/swagger/activity.json#/DetailedActivity"}, "examples": {"application/json": {"id": 12345678987654321, "resource_state": 3, "external_id": "garmin_push_12345678987654321", "upload_id": 98765432123456789, "athlete": {"id": 134815, "resource_state": 1}, "name": "Happy Friday", "distance": 28099, "moving_time": 4207, "elapsed_time": 4410, "total_elevation_gain": 516, "type": "Ride", "sport_type": "MountainBikeRide", "start_date": "2018-02-16T14:52:54Z", "start_date_local": "2018-02-16T06:52:54Z", "timezone": "(GMT-08:00) America/Los_Angeles", "utc_offset": -28800, "start_latlng": [37.83, -122.26], "end_latlng": [37.83, -122.26], "achievement_count": 0, "kudos_count": 19, "comment_count": 0, "athlete_count": 1, "photo_count": 0, "map": {"id": "a1410355832", "polyline": "ki{eFvqfiVqAWQIGEEKAYJgBVqDJ{BHa@jAkNJw@Pw@V{APs@^aABQAOEQGKoJ_FuJkFqAo@{A}@sH{DiAs@Q]?WVy@`@oBt@_CB]KYMMkB{AQEI@WT{BlE{@zAQPI@ICsCqA_BcAeCmAaFmCqIoEcLeG}KcG}A}@cDaBiDsByAkAuBqBi@y@_@o@o@kB}BgIoA_EUkAMcACa@BeBBq@LaAJe@b@uA`@_AdBcD`@iAPq@RgALqAB{@EqAyAoOCy@AmCBmANqBLqAZkB\\iCPiBJwCCsASiCq@iD]eA]y@[i@w@mAa@i@k@g@kAw@i@Ya@Q]EWFMLa@~BYpAFNpA`Aj@n@X`@V`AHh@JfB@xAMvAGZGHIDIAWOEQNcC@sACYK[MSOMe@QKKKYOs@UYQISCQ?Q@WNo@r@OHGAGCKOQ_BU}@MQGG]Io@@c@FYNg@d@s@d@ODQAMOMaASs@_@a@SESAQDqBn@a@RO?KK?UBU\\kA@Y?WMo@Iy@GWQ_@WSSGg@AkABQB_Ap@_A^o@b@Q@o@IS@OHi@n@OFS?OI}@iAQMQGQC}@DOIIUK{@IUOMyBo@kASOKIQCa@L[|AgATWN[He@?QKw@FOPCh@Fx@l@TDLELKl@aAHIJEX@r@ZTDV@LENQVg@RkA@c@MeA?WFOPMf@Ej@Fj@@LGHKDM?_@_@iC?a@HKRIl@NT?FCHMFW?YEYGWQa@GYBiAIq@Gq@L_BHSHK|@WJETSLQZs@z@_A~@uA^U`@G\\CRB\\Tl@p@Th@JZ^bB`@lAHLXVLDP?LGFSKiDBo@d@wBVi@R]VYVE\\@`@Lh@Fh@CzAk@RSDQA]GYe@eAGWSiBAWBWBIJORK`@KPOPSTg@h@}Ad@o@F[E_@EGMKUGmAEYGMIMYKs@?a@J}@@_BD_@HQJMx@e@LKHKHWAo@UoAAWFmAH}@?w@C[YwAAc@HSNM|Ao@rA}@zAq@`@a@j@eAxAuBXQj@MXSR[b@gAFg@?YISOGaAHi@Xw@v@_@d@WRSFqARUHQJc@d@m@`A[VSFUBcAEU@WFULUPa@v@Y~@UrBc@dBI~@?l@P~ABt@N`HEjA]zAEp@@p@TrBCl@CTQb@k@dAg@jAU^KJYLK@k@A[Js@d@a@b@]RgBl@[FMAw@[]G]?m@D_@F]P[Vu@t@[TMF_@Do@E_@@q@P]PWZUZw@vAkAlAGJOj@IlAMd@OR{@p@a@d@sBpD]v@a@`Aa@n@]TODgBVk@Pe@^cBfBc@Rs@La@RSPm@|@wCpDS^Wp@QZML{@l@qBbCYd@k@lAIVCZBZNTr@`@RRHZANIZQPKDW@e@CaASU?I@YTKRQx@@\\VmALYRQLCL?v@P|@D\\GJEFKDM@OCa@COOYIGm@YMUCM@]JYr@uAx@kAt@}@jAeAPWbAkBj@s@bAiAz@oAj@m@VQlAc@VQ~@aA`Au@p@Q`AIv@MZORUV_@p@iB|AoCh@q@dAaANUNWH[N{AJ[^m@t@_Av@wA\\a@`@W`@In@Al@B^E`@Wl@u@\\[VQ\\K`@Eb@?R@dAZP@d@CRExAs@\\Yt@{@LG\\MjAATINOXo@d@kAl@_AHYBOCe@QiBCm@Fq@\\wADo@AyGEeBWuB@YHu@Tu@Lk@VcCTo@d@aA\\WJE`@G~@FP?VI\\U~@sANO`@SfAMj@U\\WjAsAXS`@UNENALBHFFL?^Ml@Uj@]b@q@RUJSPkChEc@XcAb@sA|@]PaA\\OJKNER?TDTNj@Jn@?p@OfC@ZR`B@VCV_@n@{@l@WbACv@OlABnAPl@LNNHbBBNBLFFJ@^GLg@x@i@|AMP[X}@XOJKPET?l@LhAFXp@fBDRCd@S\\_@Ps@PQ@}A]S?QDe@V]b@MR[fAKt@ErAF~CANILYDKGIKe@{@Yy@e@sB[gA[c@e@YUCU?WBUHUNQPq@`AiArAMV[^e@Zc@JQJKNMz@?r@Bb@PfAAfA@VVbADn@E`@KHSEe@SMAKDKFM\\^dDCh@m@LoAQ_@@MFOZLfBEl@QbASd@KLQBOAaAc@QAQ@QHc@v@ONMJOBOCg@c@]O[EMBKFGL?RHv@ARERGNe@h@{@h@WVGNDt@JLNFPFz@LdBf@f@PJNHPF`ADPJJJDl@I`@B^Tp@bALJNDNALIf@i@PGPCt@DNE`@Uv@[dAw@RITGRCtAARBPJLPJRZxB?VEX_@vAAR?RDNHJJBh@UnBm@h@IRDRJNNJPNbBFRJLLBLCzAmAd@Uf@Gf@?P@PFJNHPFTH`BDTHNJJJ@LG`@m@^YPER@RDPHNNJRLn@HRLN^VNPHTFX@\\UlDFb@FHh@NP@HKPsB?}ASkCQ{@[y@q@}@cA{@KOCQDa@t@{CFGJCf@Nl@ZtA~@r@p@`@h@rAxBd@rA\\fARdAPjANrB?f@AtBCd@QfBkAjJOlBChA?rBFrBNlBdAfKFzAC~@Iz@Mz@Sv@s@jBmAxBi@hAWt@Sv@Qx@O`BA`@?dAPfBVpAd@`BfBlFf@fBdA~Cr@pAz@fApBhBjAt@H?IL?FBFJLx@^lHvDvh@~XnElCbAd@pGhDbAb@nAr@`Ad@`GhDnBbAxCbBrWhNJJDPARGP_@t@Qh@]pAUtAoA`Ny@jJApBBNFLJFJBv@Hb@HBF?\\", "resource_state": 3, "summary_polyline": "ki{eFvqfiVsBmA`Feh@qg@iX`B}JeCcCqGjIq~@kf@cM{KeHeX`@_GdGkSeBiXtB}YuEkPwFyDeAzAe@pC~DfGc@bIOsGmCcEiD~@oBuEkFhBcBmDiEfAVuDiAuD}NnDaNiIlCyDD_CtJKv@wGhD]YyEzBo@g@uKxGmHpCGtEtI~AuLrHkAcAaIvEgH_EaDR_FpBuBg@sNxHqEtHgLoTpIiCzKNr[sB|Es\\`JyObYeMbGsMnPsAfDxAnD}DBu@bCx@{BbEEyAoD`AmChNoQzMoGhOwX|[yIzBeFKg[zAkIdU_LiHxK}HzEh@vM_BtBg@xGzDbCcF~GhArHaIfByAhLsDiJuC?_HbHd@nL_Cz@ZnEkDDy@hHwJLiCbIrNrIvN_EfAjDWlEnEiAfBxDlFkBfBtEfDaAzBvDKdFx@|@XgJmDsHhAgD`GfElEzOwBnYdBxXgGlSc@bGdHpW|HdJztBnhAgFxc@HnCvBdA"}, "trainer": false, "commute": false, "manual": false, "private": false, "flagged": false, "gear_id": "b12345678987654321", "from_accepted_tag": false, "average_speed": 6.679, "max_speed": 18.5, "average_cadence": 78.5, "average_temp": 4, "average_watts": 185.5, "weighted_average_watts": 230, "kilojoules": 780.5, "device_watts": true, "has_heartrate": false, "max_watts": 743, "elev_high": 446.6, "elev_low": 17.2, "pr_count": 0, "total_photo_count": 2, "has_kudoed": false, "workout_type": 10, "suffer_score": null, "description": "", "calories": 870.2, "segment_efforts": [{"id": 12345678987654321, "resource_state": 2, "name": "Tunnel Rd.", "activity": {"id": 12345678987654321, "resource_state": 1}, "athlete": {"id": 134815, "resource_state": 1}, "elapsed_time": 2038, "moving_time": 2038, "start_date": "2018-02-16T14:56:25Z", "start_date_local": "2018-02-16T06:56:25Z", "distance": 9434.8, "start_index": 211, "end_index": 2246, "average_cadence": 78.6, "device_watts": true, "average_watts": 237.6, "segment": {"id": 673683, "resource_state": 2, "name": "Tunnel Rd.", "activity_type": "Ride", "distance": 9220.7, "average_grade": 4.2, "maximum_grade": 25.8, "elevation_high": 426.5, "elevation_low": 43.4, "start_latlng": [37.8346153, -122.2520872], "end_latlng": [37.8476261, -122.2008944], "climb_category": 3, "city": "Oakland", "state": "CA", "country": "United States", "private": false, "hazardous": false, "starred": false}, "kom_rank": null, "pr_rank": null, "achievements": [], "hidden": false}], "splits_metric": [{"distance": 1001.5, "elapsed_time": 141, "elevation_difference": 4.4, "moving_time": 141, "split": 1, "average_speed": 7.1, "pace_zone": 0}], "laps": [{"id": 4479306946, "resource_state": 2, "name": "Lap 1", "activity": {"id": 1410355832, "resource_state": 1}, "athlete": {"id": 134815, "resource_state": 1}, "elapsed_time": 1573, "moving_time": 1569, "start_date": "2018-02-16T14:52:54Z", "start_date_local": "2018-02-16T06:52:54Z", "distance": 8046.72, "start_index": 0, "end_index": 1570, "total_elevation_gain": 276, "average_speed": 5.12, "max_speed": 9.5, "average_cadence": 78.6, "device_watts": true, "average_watts": 233.1, "lap_index": 1, "split": 1}], "gear": {"id": "b12345678987654321", "primary": true, "name": "Tarmac", "resource_state": 2, "distance": 32547610}, "partner_brand_tag": null, "photos": {"primary": {"id": null, "unique_id": "3FDGKL3-204E-4867-9E8D-89FC79EAAE17", "urls": {"100": "https://dgtzuqphqg23d.cloudfront.net/Bv93zv5t_mr57v0wXFbY_JyvtucgmU5Ym6N9z_bKeUI-128x96.jpg", "600": "https://dgtzuqphqg23d.cloudfront.net/Bv93zv5t_mr57v0wXFbY_JyvtucgmU5Ym6N9z_bKeUI-768x576.jpg"}, "source": 1}, "use_primary_photo": true, "count": 2}, "highlighted_kudosers": [{"destination_url": "strava://athletes/12345678987654321", "display_name": "<PERSON>.", "avatar_url": "https://dgalywyr863hv.cloudfront.net/pictures/athletes/12345678987654321/12345678987654321/3/medium.jpg", "show_name": true}], "hide_from_home": false, "device_name": "Garmin Edge 1030", "embed_token": "18e4615989b47dd4ff3dc711b0aa4502e4b311a9", "segment_leaderboard_opt_out": false, "leaderboard_opt_out": false}}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}, "put": {"operationId": "updateActivityById", "summary": "Update Activity", "description": "Updates the given activity that is owned by the authenticated athlete. Requires activity:write. Also requires activity:read_all in order to update Only Me activities", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the activity.", "required": true, "type": "integer", "format": "int64"}, {"name": "body", "in": "body", "schema": {"$ref": "https://developers.strava.com/swagger/activity.json#/UpdatableActivity"}}], "tags": ["Activities"], "responses": {"200": {"description": "The activity's detailed representation.", "schema": {"$ref": "https://developers.strava.com/swagger/activity.json#/DetailedActivity"}, "examples": {"application/json": {"id": 12345678987654321, "resource_state": 3, "external_id": "garmin_push_12345678987654321", "upload_id": 98765432123456789, "athlete": {"id": 134815, "resource_state": 1}, "name": "Happy Friday", "distance": 28099, "moving_time": 4207, "elapsed_time": 4410, "total_elevation_gain": 516, "type": "Ride", "sport_type": "MountainBikeRide", "start_date": "2018-02-16T14:52:54Z", "start_date_local": "2018-02-16T06:52:54Z", "timezone": "(GMT-08:00) America/Los_Angeles", "utc_offset": -28800, "start_latlng": [37.83, -122.26], "end_latlng": [37.83, -122.26], "location_city": null, "location_state": null, "location_country": "United States", "achievement_count": 0, "kudos_count": 19, "comment_count": 0, "athlete_count": 1, "photo_count": 0, "map": {"id": "a1410355832", "polyline": "ki{eFvqfiVqAWQIGEEKAYJgBVqDJ{BHa@jAkNJw@Pw@V{APs@^aABQAOEQGKoJ_FuJkFqAo@{A}@sH{DiAs@Q]?WVy@`@oBt@_CB]KYMMkB{AQEI@WT{BlE{@zAQPI@ICsCqA_BcAeCmAaFmCqIoEcLeG}KcG}A}@cDaBiDsByAkAuBqBi@y@_@o@o@kB}BgIoA_EUkAMcACa@BeBBq@LaAJe@b@uA`@_AdBcD`@iAPq@RgALqAB{@EqAyAoOCy@AmCBmANqBLqAZkB\\iCPiBJwCCsASiCq@iD]eA]y@[i@w@mAa@i@k@g@kAw@i@Ya@Q]EWFMLa@~BYpAFNpA`Aj@n@X`@V`AHh@JfB@xAMvAGZGHIDIAWOEQNcC@sACYK[MSOMe@QKKKYOs@UYQISCQ?Q@WNo@r@OHGAGCKOQ_BU}@MQGG]Io@@c@FYNg@d@s@d@ODQAMOMaASs@_@a@SESAQDqBn@a@RO?KK?UBU\\kA@Y?WMo@Iy@GWQ_@WSSGg@AkABQB_Ap@_A^o@b@Q@o@IS@OHi@n@OFS?OI}@iAQMQGQC}@DOIIUK{@IUOMyBo@kASOKIQCa@L[|AgATWN[He@?QKw@FOPCh@Fx@l@TDLELKl@aAHIJEX@r@ZTDV@LENQVg@RkA@c@MeA?WFOPMf@Ej@Fj@@LGHKDM?_@_@iC?a@HKRIl@NT?FCHMFW?YEYGWQa@GYBiAIq@Gq@L_BHSHK|@WJETSLQZs@z@_A~@uA^U`@G\\CRB\\Tl@p@Th@JZ^bB`@lAHLXVLDP?LGFSKiDBo@d@wBVi@R]VYVE\\@`@Lh@Fh@CzAk@RSDQA]GYe@eAGWSiBAWBWBIJORK`@KPOPSTg@h@}Ad@o@F[E_@EGMKUGmAEYGMIMYKs@?a@J}@@_BD_@HQJMx@e@LKHKHWAo@UoAAWFmAH}@?w@C[YwAAc@HSNM|Ao@rA}@zAq@`@a@j@eAxAuBXQj@MXSR[b@gAFg@?YISOGaAHi@Xw@v@_@d@WRSFqARUHQJc@d@m@`A[VSFUBcAEU@WFULUPa@v@Y~@UrBc@dBI~@?l@P~ABt@N`HEjA]zAEp@@p@TrBCl@CTQb@k@dAg@jAU^KJYLK@k@A[Js@d@a@b@]RgBl@[FMAw@[]G]?m@D_@F]P[Vu@t@[TMF_@Do@E_@@q@P]PWZUZw@vAkAlAGJOj@IlAMd@OR{@p@a@d@sBpD]v@a@`Aa@n@]TODgBVk@Pe@^cBfBc@Rs@La@RSPm@|@wCpDS^Wp@QZML{@l@qBbCYd@k@lAIVCZBZNTr@`@RRHZANIZQPKDW@e@CaASU?I@YTKRQx@@\\VmALYRQLCL?v@P|@D\\GJEFKDM@OCa@COOYIGm@YMUCM@]JYr@uAx@kAt@}@jAeAPWbAkBj@s@bAiAz@oAj@m@VQlAc@VQ~@aA`Au@p@Q`AIv@MZORUV_@p@iB|AoCh@q@dAaANUNWH[N{AJ[^m@t@_Av@wA\\a@`@W`@In@Al@B^E`@Wl@u@\\[VQ\\K`@Eb@?R@dAZP@d@CRExAs@\\Yt@{@LG\\MjAATINOXo@d@kAl@_AHYBOCe@QiBCm@Fq@\\wADo@AyGEeBWuB@YHu@Tu@Lk@VcCTo@d@aA\\WJE`@G~@FP?VI\\U~@sANO`@SfAMj@U\\WjAsAXS`@UNENALBHFFL?^Ml@Uj@]b@q@RUJSPkChEc@XcAb@sA|@]PaA\\OJKNER?TDTNj@Jn@?p@OfC@ZR`B@VCV_@n@{@l@WbACv@OlABnAPl@LNNHbBBNBLFFJ@^GLg@x@i@|AMP[X}@XOJKPET?l@LhAFXp@fBDRCd@S\\_@Ps@PQ@}A]S?QDe@V]b@MR[fAKt@ErAF~CANILYDKGIKe@{@Yy@e@sB[gA[c@e@YUCU?WBUHUNQPq@`AiArAMV[^e@Zc@JQJKNMz@?r@Bb@PfAAfA@VVbADn@E`@KHSEe@SMAKDKFM\\^dDCh@m@LoAQ_@@MFOZLfBEl@QbASd@KLQBOAaAc@QAQ@QHc@v@ONMJOBOCg@c@]O[EMBKFGL?RHv@ARERGNe@h@{@h@WVGNDt@JLNFPFz@LdBf@f@PJNHPF`ADPJJJDl@I`@B^Tp@bALJNDNALIf@i@PGPCt@DNE`@Uv@[dAw@RITGRCtAARBPJLPJRZxB?VEX_@vAAR?RDNHJJBh@UnBm@h@IRDRJNNJPNbBFRJLLBLCzAmAd@Uf@Gf@?P@PFJNHPFTH`BDTHNJJJ@LG`@m@^YPER@RDPHNNJRLn@HRLN^VNPHTFX@\\UlDFb@FHh@NP@HKPsB?}ASkCQ{@[y@q@}@cA{@KOCQDa@t@{CFGJCf@Nl@ZtA~@r@p@`@h@rAxBd@rA\\fARdAPjANrB?f@AtBCd@QfBkAjJOlBChA?rBFrBNlBdAfKFzAC~@Iz@Mz@Sv@s@jBmAxBi@hAWt@Sv@Qx@O`BA`@?dAPfBVpAd@`BfBlFf@fBdA~Cr@pAz@fApBhBjAt@H?IL?FBFJLx@^lHvDvh@~XnElCbAd@pGhDbAb@nAr@`Ad@`GhDnBbAxCbBrWhNJJDPARGP_@t@Qh@]pAUtAoA`Ny@jJApBBNFLJFJBv@Hb@HBF?\\", "resource_state": 3, "summary_polyline": "ki{eFvqfiVsBmA`Feh@qg@iX`B}JeCcCqGjIq~@kf@cM{KeHeX`@_GdGkSeBiXtB}YuEkPwFyDeAzAe@pC~DfGc@bIOsGmCcEiD~@oBuEkFhBcBmDiEfAVuDiAuD}NnDaNiIlCyDD_CtJKv@wGhD]YyEzBo@g@uKxGmHpCGtEtI~AuLrHkAcAaIvEgH_EaDR_FpBuBg@sNxHqEtHgLoTpIiCzKNr[sB|Es\\`JyObYeMbGsMnPsAfDxAnD}DBu@bCx@{BbEEyAoD`AmChNoQzMoGhOwX|[yIzBeFKg[zAkIdU_LiHxK}HzEh@vM_BtBg@xGzDbCcF~GhArHaIfByAhLsDiJuC?_HbHd@nL_Cz@ZnEkDDy@hHwJLiCbIrNrIvN_EfAjDWlEnEiAfBxDlFkBfBtEfDaAzBvDKdFx@|@XgJmDsHhAgD`GfElEzOwBnYdBxXgGlSc@bGdHpW|HdJztBnhAgFxc@HnCvBdA"}, "trainer": false, "commute": false, "manual": false, "private": false, "flagged": false, "gear_id": "b12345678987654321", "from_accepted_tag": false, "average_speed": 6.679, "max_speed": 18.5, "average_cadence": 78.5, "average_temp": 4, "average_watts": 185.5, "weighted_average_watts": 230, "kilojoules": 780.5, "device_watts": true, "has_heartrate": false, "max_watts": 743, "elev_high": 446.6, "elev_low": 17.2, "pr_count": 0, "total_photo_count": 2, "has_kudoed": false, "workout_type": 10, "suffer_score": null, "description": "", "calories": 870.2, "segment_efforts": [{"id": 12345678987654321, "resource_state": 2, "name": "Tunnel Rd.", "activity": {"id": 12345678987654321, "resource_state": 1}, "athlete": {"id": 12345678987654321, "resource_state": 1}, "elapsed_time": 2038, "moving_time": 2038, "start_date": "2018-02-16T14:56:25Z", "start_date_local": "2018-02-16T06:56:25Z", "distance": 9434.8, "start_index": 211, "end_index": 2246, "average_cadence": 78.6, "device_watts": true, "average_watts": 237.6, "segment": {"id": 673683, "resource_state": 2, "name": "Tunnel Rd.", "activity_type": "Ride", "distance": 9220.7, "average_grade": 4.2, "maximum_grade": 25.8, "elevation_high": 426.5, "elevation_low": 43.4, "start_latlng": [37.8346153, -122.2520872], "end_latlng": [37.8476261, -122.2008944], "climb_category": 3, "city": "Oakland", "state": "CA", "country": "United States", "private": false, "hazardous": false, "starred": false}, "kom_rank": null, "pr_rank": null, "achievements": [], "hidden": false}], "splits_metric": [{"distance": 1001.5, "elapsed_time": 141, "elevation_difference": 4.4, "moving_time": 141, "split": 1, "average_speed": 7.1, "pace_zone": 0}], "laps": [{"id": 4479306946, "resource_state": 2, "name": "Lap 1", "activity": {"id": 1410355832, "resource_state": 1}, "athlete": {"id": 134815, "resource_state": 1}, "elapsed_time": 1573, "moving_time": 1569, "start_date": "2018-02-16T14:52:54Z", "start_date_local": "2018-02-16T06:52:54Z", "distance": 8046.72, "start_index": 0, "end_index": 1570, "total_elevation_gain": 276, "average_speed": 5.12, "max_speed": 9.5, "average_cadence": 78.6, "device_watts": true, "average_watts": 233.1, "lap_index": 1, "split": 1}], "gear": {"id": "b12345678987654321", "primary": true, "name": "Tarmac", "resource_state": 2, "distance": 32547610}, "partner_brand_tag": null, "photos": {"primary": {"id": null, "unique_id": "3FDGKL3-204E-4867-9E8D-89FC79EAAE17", "urls": {"100": "https://dgtzuqphqg23d.cloudfront.net/Bv93zv5t_mr57v0wXFbY_JyvtucgmU5Ym6N9z_bKeUI-128x96.jpg", "600": "https://dgtzuqphqg23d.cloudfront.net/Bv93zv5t_mr57v0wXFbY_JyvtucgmU5Ym6N9z_bKeUI-768x576.jpg"}, "source": 1}, "use_primary_photo": true, "count": 2}, "highlighted_kudosers": [{"destination_url": "strava://athletes/12345678987654321", "display_name": "<PERSON>.", "avatar_url": "https://dgalywyr863hv.cloudfront.net/pictures/athletes/12345678987654321/12345678987654321/3/medium.jpg", "show_name": true}], "hide_from_home": false, "device_name": "Garmin Edge 1030", "embed_token": "18e4615989b47dd4ff3dc711b0aa4502e4b311a9", "segment_leaderboard_opt_out": false, "leaderboard_opt_out": false}}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/athlete/activities": {"get": {"operationId": "getLoggedInAthleteActivities", "summary": "List Athlete Activities", "description": "Returns the activities of an athlete for a specific identifier. Requires activity:read. Only Me activities will be filtered out unless requested by a token with activity:read_all.", "parameters": [{"name": "before", "in": "query", "description": "An epoch timestamp to use for filtering activities that have taken place before a certain time.", "type": "integer"}, {"name": "after", "in": "query", "description": "An epoch timestamp to use for filtering activities that have taken place after a certain time.", "type": "integer"}, {"$ref": "#/parameters/page"}, {"$ref": "#/parameters/perPage"}], "tags": ["Activities"], "responses": {"200": {"description": "The authenticated athlete's activities", "schema": {"type": "array", "items": {"$ref": "https://developers.strava.com/swagger/activity.json#/SummaryActivity"}}, "examples": {"application/json": [{"resource_state": 2, "athlete": {"id": 134815, "resource_state": 1}, "name": "Happy Friday", "distance": 24931.4, "moving_time": 4500, "elapsed_time": 4500, "total_elevation_gain": 0, "type": "Ride", "sport_type": "MountainBikeRide", "workout_type": null, "id": 154504250376823, "external_id": "garmin_push_12345678987654321", "upload_id": 987654321234567891234, "start_date": "2018-05-02T12:15:09Z", "start_date_local": "2018-05-02T05:15:09Z", "timezone": "(GMT-08:00) America/Los_Angeles", "utc_offset": -25200, "start_latlng": null, "end_latlng": null, "location_city": null, "location_state": null, "location_country": "United States", "achievement_count": 0, "kudos_count": 3, "comment_count": 1, "athlete_count": 1, "photo_count": 0, "map": {"id": "a12345678987654321", "summary_polyline": null, "resource_state": 2}, "trainer": true, "commute": false, "manual": false, "private": false, "flagged": false, "gear_id": "b12345678987654321", "from_accepted_tag": false, "average_speed": 5.54, "max_speed": 11, "average_cadence": 67.1, "average_watts": 175.3, "weighted_average_watts": 210, "kilojoules": 788.7, "device_watts": true, "has_heartrate": true, "average_heartrate": 140.3, "max_heartrate": 178, "max_watts": 406, "pr_count": 0, "total_photo_count": 1, "has_kudoed": false, "suffer_score": 82}, {"resource_state": 2, "athlete": {"id": 167560, "resource_state": 1}, "name": "<PERSON><PERSON>", "distance": 23676.5, "moving_time": 5400, "elapsed_time": 5400, "total_elevation_gain": 0, "type": "Ride", "sport_type": "MountainBikeRide", "workout_type": null, "id": 1234567809, "external_id": "garmin_push_12345678987654321", "upload_id": 1234567819, "start_date": "2018-04-30T12:35:51Z", "start_date_local": "2018-04-30T05:35:51Z", "timezone": "(GMT-08:00) America/Los_Angeles", "utc_offset": -25200, "start_latlng": null, "end_latlng": null, "location_city": null, "location_state": null, "location_country": "United States", "achievement_count": 0, "kudos_count": 4, "comment_count": 0, "athlete_count": 1, "photo_count": 0, "map": {"id": "a12345689", "summary_polyline": null, "resource_state": 2}, "trainer": true, "commute": false, "manual": false, "private": false, "flagged": false, "gear_id": "b12345678912343", "from_accepted_tag": false, "average_speed": 4.385, "max_speed": 8.8, "average_cadence": 69.8, "average_watts": 200, "weighted_average_watts": 214, "kilojoules": 1080, "device_watts": true, "has_heartrate": true, "average_heartrate": 152.4, "max_heartrate": 183, "max_watts": 403, "pr_count": 0, "total_photo_count": 1, "has_kudoed": false, "suffer_score": 162}]}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/activities/{id}/laps": {"get": {"operationId": "getLapsByActivityId", "summary": "List Activity Laps", "description": "Returns the laps of an activity identified by an identifier. Requires activity:read for Everyone and Followers activities. Requires activity:read_all for Only Me activities.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the activity.", "required": true, "type": "integer", "format": "int64"}], "tags": ["Activities"], "responses": {"200": {"description": "Activity Laps.", "schema": {"type": "array", "items": {"$ref": "https://developers.strava.com/swagger/lap.json#/Lap"}}, "examples": {"application/json": [{"id": 12345678987654321, "resource_state": 2, "name": "Lap 1", "activity": {"id": 12345678987654321, "resource_state": 1}, "athlete": {"id": 12345678987654321, "resource_state": 1}, "elapsed_time": 1691, "moving_time": 1587, "start_date": "2018-02-08T14:13:37Z", "start_date_local": "2018-02-08T06:13:37Z", "distance": 8046.72, "start_index": 0, "end_index": 1590, "total_elevation_gain": 270, "average_speed": 4.76, "max_speed": 9.4, "average_cadence": 79, "device_watts": true, "average_watts": 228.2, "lap_index": 1, "split": 1}]}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/activities/{id}/zones": {"get": {"operationId": "getZonesByActivityId", "summary": "Get Activity Zones", "description": "Summit Feature. Returns the zones of a given activity. Requires activity:read for Everyone and Followers activities. Requires activity:read_all for Only Me activities.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the activity.", "required": true, "type": "integer", "format": "int64"}], "tags": ["Activities"], "responses": {"200": {"description": "Activity Zones.", "schema": {"type": "array", "items": {"$ref": "https://developers.strava.com/swagger/zones.json#/ActivityZone"}}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/activities/{id}/comments": {"get": {"operationId": "getCommentsByActivityId", "summary": "List Activity Comments", "description": "Returns the comments on the given activity. Requires activity:read for Everyone and Followers activities. Requires activity:read_all for Only Me activities.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the activity.", "required": true, "type": "integer", "format": "int64"}, {"name": "page", "in": "query", "description": "Deprecated. Prefer to use after_cursor.", "type": "integer"}, {"name": "per_page", "in": "query", "description": "Deprecated. Prefer to use page_size.", "type": "integer", "default": 30}, {"name": "page_size", "in": "query", "description": "Number of items per page. Defaults to 30.", "type": "integer", "default": 30}, {"name": "after_cursor", "in": "query", "description": "Cursor of the last item in the previous page of results, used to request the subsequent page of results.  When omitted, the first page of results is fetched.", "type": "string"}], "tags": ["Activities"], "responses": {"200": {"description": "Comments.", "schema": {"type": "array", "items": {"$ref": "https://developers.strava.com/swagger/comment.json#/Comment"}}, "examples": {"application/json": [{"id": 12345678987654321, "activity_id": 12345678987654321, "post_id": null, "resource_state": 2, "text": "Good job and keep the cat pictures coming!", "mentions_metadata": null, "created_at": "2018-02-08T19:25:39Z", "athlete": {"firstname": "<PERSON>", "lastname": "S"}, "cursor": "abc123%20"}]}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/activities/{id}/kudos": {"get": {"operationId": "getKudoersByActivityId", "summary": "List Activity Kudoers", "description": "Returns the athletes who kudoed an activity identified by an identifier. Requires activity:read for Everyone and Followers activities. Requires activity:read_all for Only Me activities.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the activity.", "required": true, "type": "integer", "format": "int64"}, {"$ref": "#/parameters/page"}, {"$ref": "#/parameters/perPage"}], "tags": ["Activities"], "responses": {"200": {"description": "Comments.", "schema": {"type": "array", "items": {"$ref": "https://developers.strava.com/swagger/athlete.json#/SummaryAthlete"}}, "examples": {"application/json": [{"firstname": "<PERSON>", "lastname": "S"}]}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/clubs/{id}": {"get": {"operationId": "getClubById", "summary": "Get Club", "description": "Returns a given club using its identifier.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the club.", "required": true, "type": "integer", "format": "int64"}], "tags": ["Clubs"], "responses": {"200": {"description": "The detailed representation of a club.", "schema": {"$ref": "https://developers.strava.com/swagger/club.json#/DetailedClub"}, "examples": {"application/json": {"id": 1, "resource_state": 3, "name": "Team Strava Cycling", "profile_medium": "https://dgalywyr863hv.cloudfront.net/pictures/clubs/1/1582/4/medium.jpg", "profile": "https://dgalywyr863hv.cloudfront.net/pictures/clubs/1/1582/4/large.jpg", "cover_photo": "https://dgalywyr863hv.cloudfront.net/pictures/clubs/1/4328276/1/large.jpg", "cover_photo_small": "https://dgalywyr863hv.cloudfront.net/pictures/clubs/1/4328276/1/small.jpg", "sport_type": "cycling", "activity_types": ["Ride", "VirtualRide", "EBikeRide", "Velomobile", "Handcycle"], "city": "San Francisco", "state": "California", "country": "United States", "private": true, "member_count": 116, "featured": false, "verified": false, "url": "team-strava-bike", "membership": "member", "admin": false, "owner": false, "description": "Private club for Cyclists who work at Strava.", "club_type": "company", "post_count": 29, "owner_id": 759, "following_count": 107}}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/clubs/{id}/members": {"get": {"operationId": "getClubMembersById", "summary": "List Club Members", "description": "Returns a list of the athletes who are members of a given club.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the club.", "required": true, "type": "integer", "format": "int64"}, {"$ref": "#/parameters/page"}, {"$ref": "#/parameters/perPage"}], "tags": ["Clubs"], "responses": {"200": {"description": "A list of club athlete representations.", "schema": {"type": "array", "items": {"$ref": "https://developers.strava.com/swagger/athlete.json#/ClubAthlete"}}, "examples": {"application/json": [{"resource_state": 2, "firstname": "<PERSON>", "lastname": "S.", "membership": "member", "admin": false, "owner": false}]}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/clubs/{id}/admins": {"get": {"operationId": "getClubAdminsById", "summary": "List Club Administrators", "description": "Returns a list of the administrators of a given club.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the club.", "required": true, "type": "integer", "format": "int64"}, {"$ref": "#/parameters/page"}, {"$ref": "#/parameters/perPage"}], "tags": ["Clubs"], "responses": {"200": {"description": "A list of summary athlete representations.", "schema": {"type": "array", "items": {"$ref": "https://developers.strava.com/swagger/athlete.json#/SummaryAthlete"}}, "examples": {"application/json": [{"resource_state": 2, "firstname": "<PERSON>", "lastname": "S."}]}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/clubs/{id}/activities": {"get": {"operationId": "getClubActivitiesById", "summary": "List Club Activities", "description": "Retrieve recent activities from members of a specific club. The authenticated athlete must belong to the requested club in order to hit this endpoint. Pagination is supported. Athlete profile visibility is respected for all activities.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the club.", "required": true, "type": "integer", "format": "int64"}, {"$ref": "#/parameters/page"}, {"$ref": "#/parameters/perPage"}], "tags": ["Clubs"], "responses": {"200": {"description": "A list of activities.", "schema": {"type": "array", "items": {"$ref": "https://developers.strava.com/swagger/activity.json#/ClubActivity"}}, "examples": {"application/json": [{"resource_state": 2, "athlete": {"resource_state": 2, "firstname": "<PERSON>", "lastname": "S."}, "name": "World Championship", "distance": 2641.7, "moving_time": 577, "elapsed_time": 635, "total_elevation_gain": 8.8, "type": "Ride", "sport_type": "MountainBikeRide", "workout_type": null}]}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/athlete/clubs": {"get": {"operationId": "getLoggedInAthleteClubs", "summary": "List Athlete Clubs", "description": "Returns a list of the clubs whose membership includes the authenticated athlete.", "parameters": [{"$ref": "#/parameters/page"}, {"$ref": "#/parameters/perPage"}], "tags": ["Clubs"], "responses": {"200": {"description": "A list of summary club representations.", "schema": {"type": "array", "items": {"$ref": "https://developers.strava.com/swagger/club.json#/SummaryClub"}}, "examples": {"application/json": [{"id": 231407, "resource_state": 2, "name": "The Strava Club", "profile_medium": "https://dgalywyr863hv.cloudfront.net/pictures/clubs/231407/5319085/1/medium.jpg", "profile": "https://dgalywyr863hv.cloudfront.net/pictures/clubs/231407/5319085/1/large.jpg", "cover_photo": "https://dgalywyr863hv.cloudfront.net/pictures/clubs/231407/5098428/4/large.jpg", "cover_photo_small": "https://dgalywyr863hv.cloudfront.net/pictures/clubs/231407/5098428/4/small.jpg", "sport_type": "other", "city": "San Francisco", "state": "California", "country": "United States", "private": false, "member_count": 93151, "featured": false, "verified": true, "url": "strava"}]}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/gear/{id}": {"get": {"operationId": "getGearById", "summary": "Get Equipment", "description": "Returns an equipment using its identifier.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the gear.", "required": true, "type": "string"}], "tags": ["Gears"], "responses": {"200": {"description": "A representation of the gear.", "schema": {"$ref": "https://developers.strava.com/swagger/gear.json#/DetailedGear"}, "examples": {"application/json": {"id": "b1231", "primary": false, "resource_state": 3, "distance": 388206, "brand_name": "BMC", "model_name": "Teammachine", "frame_type": 3, "description": "My Bike."}}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/routes/{id}": {"get": {"operationId": "getRouteById", "summary": "Get Route", "description": "Returns a route using its identifier. Requires read_all scope for private routes.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the route.", "required": true, "type": "integer", "format": "int64"}], "tags": ["Routes"], "responses": {"200": {"description": "A representation of the route.", "schema": {"$ref": "https://developers.strava.com/swagger/route.json#/Route"}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/athletes/{id}/routes": {"get": {"operationId": "getRoutesByAthleteId", "summary": "List Athlete Routes", "description": "Returns a list of the routes created by the authenticated athlete. Private routes are filtered out unless requested by a token with read_all scope.", "parameters": [{"$ref": "#/parameters/page"}, {"$ref": "#/parameters/perPage"}], "tags": ["Routes"], "responses": {"200": {"description": "A representation of the route.", "schema": {"type": "array", "items": {"$ref": "https://developers.strava.com/swagger/route.json#/Route"}}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/routes/{id}/export_gpx": {"get": {"operationId": "getRouteAsGPX", "summary": "Export Route GPX", "description": "Returns a GPX file of the route. Requires read_all scope for private routes.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the route.", "required": true, "type": "integer", "format": "int64"}], "tags": ["Routes"], "responses": {"200": {"description": "A GPX file with the route.", "content": {"application/gpx+xml": {"schema": {"type": "string", "format": "binary"}}}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/routes/{id}/export_tcx": {"get": {"operationId": "getRouteAsTCX", "summary": "Export Route TCX", "description": "Returns a TCX file of the route. Requires read_all scope for private routes.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the route.", "required": true, "type": "integer", "format": "int64"}], "tags": ["Routes"], "responses": {"200": {"description": "A TCX file with the route.", "content": {"application/tcx+xml": {"schema": {"type": "string", "format": "binary"}}}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/uploads": {"post": {"operationId": "createUpload", "summary": "Upload Activity", "description": "Uploads a new data file to create an activity from. Requires activity:write scope.", "consumes": ["multipart/form-data"], "parameters": [{"name": "file", "in": "formData", "type": "file", "description": "The uploaded file."}, {"name": "name", "in": "formData", "description": "The desired name of the resulting activity.", "type": "string"}, {"name": "description", "in": "formData", "description": "The desired description of the resulting activity.", "type": "string"}, {"name": "trainer", "in": "formData", "description": "Whether the resulting activity should be marked as having been performed on a trainer.", "type": "string"}, {"name": "commute", "in": "formData", "description": "Whether the resulting activity should be tagged as a commute.", "type": "string"}, {"name": "data_type", "in": "formData", "description": "The format of the uploaded file.", "type": "string", "enum": ["fit", "fit.gz", "tcx", "tcx.gz", "gpx", "gpx.gz"]}, {"name": "external_id", "in": "formData", "description": "The desired external identifier of the resulting activity.", "type": "string"}], "tags": ["Uploads"], "responses": {"201": {"description": "A representation of the created upload.", "schema": {"$ref": "https://developers.strava.com/swagger/upload.json#/Upload"}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/uploads/{uploadId}": {"get": {"operationId": "getUploadById", "summary": "Get Upload", "description": "Returns an upload for a given identifier. Requires activity:write scope.", "parameters": [{"name": "uploadId", "in": "path", "description": "The identifier of the upload.", "required": true, "type": "integer", "format": "int64"}], "tags": ["Uploads"], "responses": {"200": {"description": "Representation of the upload.", "schema": {"$ref": "https://developers.strava.com/swagger/upload.json#/Upload"}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/activities/{id}/streams": {"get": {"operationId": "getActivityStreams", "summary": "Get Activity Streams", "description": "Returns the given activity's streams. Requires activity:read scope. Requires activity:read_all scope for Only Me activities.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the activity.", "required": true, "type": "integer", "format": "int64"}, {"name": "keys", "in": "query", "description": "Desired stream types.", "required": true, "type": "array", "items": {"type": "string", "enum": ["time", "distance", "latlng", "altitude", "velocity_smooth", "heartrate", "cadence", "watts", "temp", "moving", "grade_smooth"]}, "collectionFormat": "csv", "minItems": 1}, {"name": "key_by_type", "in": "query", "description": "Must be true.", "type": "boolean", "required": true, "default": true}], "tags": ["Streams"], "responses": {"200": {"description": "The set of requested streams.", "schema": {"$ref": "https://developers.strava.com/swagger/stream.json#/StreamSet"}, "examples": {"application/json": [{"type": "distance", "data": [2.9, 5.8, 8.5, 11.7, 15, 19, 23.2, 28, 32.8, 38.1, 43.8, 49.5], "series_type": "distance", "original_size": 12, "resolution": "high"}]}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/segment_efforts/{id}/streams": {"get": {"operationId": "getSegmentEffortStreams", "summary": "Get Segment Effort Streams", "description": "Returns a set of streams for a segment effort completed by the authenticated athlete. Requires read_all scope.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the segment effort.", "required": true, "type": "integer", "format": "int64"}, {"name": "keys", "in": "query", "description": "The types of streams to return.", "required": true, "type": "array", "items": {"type": "string", "enum": ["time", "distance", "latlng", "altitude", "velocity_smooth", "heartrate", "cadence", "watts", "temp", "moving", "grade_smooth"]}, "collectionFormat": "csv", "minItems": 1}, {"name": "key_by_type", "in": "query", "description": "Must be true.", "type": "boolean", "required": true, "default": true}], "tags": ["Streams"], "responses": {"200": {"description": "The set of requested streams.", "schema": {"$ref": "https://developers.strava.com/swagger/stream.json#/StreamSet"}, "examples": {"application/json": [{"type": "distance", "data": [904.5, 957.8, 963.1, 989.1, 1011.9, 1049.7, 1082.4, 1098.1, 1113.2, 1124.7, 1139.2, 1142.1, 1170.4, 1173], "series_type": "distance", "original_size": 14, "resolution": "high"}]}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/segments/{id}/streams": {"get": {"operationId": "getSegmentStreams", "summary": "Get Segment Streams", "description": "Returns the given segment's streams. Requires read_all scope for private segments.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the segment.", "required": true, "type": "integer", "format": "int64"}, {"name": "keys", "in": "query", "description": "The types of streams to return.", "required": true, "type": "array", "items": {"type": "string", "enum": ["distance", "latlng", "altitude"]}, "collectionFormat": "csv", "minItems": 1}, {"name": "key_by_type", "in": "query", "description": "Must be true.", "type": "boolean", "required": true, "default": true}], "tags": ["Streams"], "responses": {"200": {"description": "The set of requested streams.", "schema": {"$ref": "https://developers.strava.com/swagger/stream.json#/StreamSet"}, "examples": {"application/json": [{"type": "latlng", "data": [[37.833112, -122.483436], [37.832964, -122.483406]], "series_type": "distance", "original_size": 2, "resolution": "high"}, {"type": "distance", "data": [0, 16.8], "series_type": "distance", "original_size": 2, "resolution": "high"}, {"type": "altitude", "data": [92.4, 93.4], "series_type": "distance", "original_size": 2, "resolution": "high"}]}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}, "/routes/{id}/streams": {"get": {"operationId": "getRouteStreams", "summary": "Get Route Streams", "description": "Returns the given route's streams. Requires read_all scope for private routes.", "parameters": [{"name": "id", "in": "path", "description": "The identifier of the route.", "required": true, "type": "integer", "format": "int64"}], "tags": ["Streams"], "responses": {"200": {"description": "The set of requested streams.", "schema": {"$ref": "https://developers.strava.com/swagger/stream.json#/StreamSet"}, "examples": {"application/json": [{"type": "latlng", "data": [[37.833112, -122.483436], [37.832964, -122.483406]]}, {"type": "distance", "data": [0, 16.8]}, {"type": "altitude", "data": [92.4, 93.4]}]}}, "default": {"description": "Unexpected error.", "schema": {"$ref": "https://developers.strava.com/swagger/fault.json#/Fault"}}}}}}}